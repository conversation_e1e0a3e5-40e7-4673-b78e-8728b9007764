import { createRouter, createWebHistory } from 'vue-router'
import Layout from '../components/Layout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/',
      component: Layout,
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('../views/Dashboard.vue')
        },
        // 客户投诉管理模块
        {
          path: 'complaints/list',
          name: 'ComplaintsList',
          component: () => import('../views/complaints/ComplaintsList.vue')
        },
        {
          path: 'complaints/add',
          name: 'ComplaintsAdd',
          component: () => import('../views/complaints/ComplaintsAdd.vue')
        },
        {
          path: 'complaints/detail/:id',
          name: 'ComplaintDetail',
          component: () => import('../views/complaints/ComplaintDetail.vue')
        },
        // 返工数据管理模块
        {
          path: 'rework/list',
          name: 'ReworkList',
          component: () => import('../views/rework/ReworkList.vue')
        },
        {
          path: 'rework/cost',
          name: 'ReworkCost',
          component: () => import('../views/rework/ReworkCost.vue')
        },
        {
          path: 'rework/detail/:id',
          name: 'ReworkDetail',
          component: () => import('../views/rework/ReworkDetail.vue')
        },
        // 原因分析对策模块
        {
          path: 'analysis/list',
          name: 'AnalysisList',
          component: () => import('../views/analysis/AnalysisList.vue')
        },
        {
          path: 'analysis/classification',
          name: 'AnalysisClassification',
          component: () => import('../views/analysis/AnalysisClassification.vue')
        },
        // 统计分析报表模块
        {
          path: 'statistics/dashboard',
          name: 'StatisticsDashboard',
          component: () => import('../views/statistics/StatisticsDashboard.vue')
        },
        {
          path: 'statistics/trends',
          name: 'StatisticsTrends',
          component: () => import('../views/statistics/StatisticsTrends.vue')
        },
        {
          path: 'statistics/pareto',
          name: 'StatisticsPareto',
          component: () => import('../views/statistics/StatisticsPareto.vue')
        }
      ]
    }
  ]
})

export default router
