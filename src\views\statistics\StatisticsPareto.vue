<template>
  <div class="statistics-pareto">
    <div class="page-header">
      <h2>柏拉图分析</h2>
      <div>
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出分析
        </el-button>
      </div>
    </div>

    <!-- 分析配置 -->
    <el-card class="config-card">
      <el-form :model="configForm" inline>
        <el-form-item label="分析类型">
          <el-select v-model="configForm.analysisType" placeholder="请选择分析类型" @change="handleTypeChange">
            <el-option label="不良类型分析" value="defectType" />
            <el-option label="产品型号分析" value="product" />
            <el-option label="客户分析" value="customer" />
            <el-option label="根本原因分析" value="rootCause" />
            <el-option label="工厂分析" value="factory" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="configForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="数据指标">
          <el-select v-model="configForm.metric" placeholder="请选择数据指标">
            <el-option label="投诉数量" value="count" />
            <el-option label="不良率" value="defectRate" />
            <el-option label="返工成本" value="cost" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleGenerate">
            <el-icon><TrendCharts /></el-icon>
            生成柏拉图
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 柏拉图图表 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>{{ chartTitle }}</span>
              <div>
                <el-tag type="info">80/20法则</el-tag>
                <el-button type="text" @click="handleSwitchView">
                  {{ showTable ? '图表视图' : '表格视图' }}
                </el-button>
              </div>
            </div>
          </template>
          
          <!-- 图表视图 -->
          <div v-show="!showTable" class="chart-container">
            <v-chart :option="paretoOption" style="height: 500px;" />
          </div>
          
          <!-- 表格视图 -->
          <div v-show="showTable" class="table-container">
            <el-table :data="paretoData" style="width: 100%">
              <el-table-column type="index" label="排名" width="60" />
              <el-table-column prop="name" :label="getColumnLabel('name')" width="150" />
              <el-table-column prop="value" :label="getColumnLabel('value')" width="120">
                <template #default="scope">
                  {{ formatValue(scope.row.value) }}
                </template>
              </el-table-column>
              <el-table-column prop="percentage" label="占比(%)" width="100">
                <template #default="scope">
                  {{ scope.row.percentage }}%
                </template>
              </el-table-column>
              <el-table-column prop="cumulativePercentage" label="累计占比(%)" width="120">
                <template #default="scope">
                  <span :class="scope.row.cumulativePercentage <= 80 ? 'key-factor' : ''">
                    {{ scope.row.cumulativePercentage }}%
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="category" label="分类" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.cumulativePercentage <= 80 ? 'danger' : 'info'">
                    {{ scope.row.cumulativePercentage <= 80 ? '关键因子' : '一般因子' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="improvement" label="改善建议" min-width="200" show-overflow-tooltip />
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分析结果 -->
    <el-row :gutter="20" class="analysis-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>关键因子分析</span>
          </template>
          <div class="key-factors">
            <div class="factor-summary">
              <p><strong>80%的问题由以下 {{ keyFactors.length }} 个关键因子造成：</strong></p>
              <ul>
                <li v-for="factor in keyFactors" :key="factor.name">
                  <span class="factor-name">{{ factor.name }}</span>
                  <span class="factor-value">{{ formatValue(factor.value) }}</span>
                  <span class="factor-percentage">({{ factor.percentage }}%)</span>
                </li>
              </ul>
            </div>
            <div class="improvement-priority">
              <h4>改善优先级建议：</h4>
              <ol>
                <li v-for="(factor, index) in keyFactors" :key="index">
                  优先解决 <strong>{{ factor.name }}</strong> 问题，预期可减少 {{ factor.percentage }}% 的投诉
                </li>
              </ol>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>统计摘要</span>
          </template>
          <div class="statistics-summary">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="总数据量">{{ summary.totalCount }}</el-descriptions-item>
              <el-descriptions-item label="分析项目数">{{ summary.itemCount }}</el-descriptions-item>
              <el-descriptions-item label="关键因子数">{{ summary.keyFactorCount }}</el-descriptions-item>
              <el-descriptions-item label="关键因子占比">{{ summary.keyFactorRatio }}%</el-descriptions-item>
              <el-descriptions-item label="关键因子贡献度">{{ summary.keyFactorContribution }}%</el-descriptions-item>
              <el-descriptions-item label="改善潜力">{{ summary.improvementPotential }}%</el-descriptions-item>
            </el-descriptions>
            
            <div class="action-recommendations" style="margin-top: 20px;">
              <h4>行动建议：</h4>
              <el-alert
                :title="getActionRecommendation()"
                type="info"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 历史对比 -->
    <el-card class="history-card">
      <template #header>
        <span>历史对比分析</span>
      </template>
      <div class="chart-container">
        <v-chart :option="historyComparisonOption" style="height: 350px;" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { ElMessage } from 'element-plus'

use([
  CanvasRenderer,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 配置表单
const configForm = ref({
  analysisType: 'defectType',
  dateRange: [],
  metric: 'count'
})

// 视图切换
const showTable = ref(false)

// 柏拉图数据
const paretoData = ref([])

// 示例数据
const mockData = {
  defectType: [
    { name: '结构问题', value: 25, percentage: 35.7, cumulativePercentage: 35.7, improvement: '加强设计评审，优化结构设计' },
    { name: '外观问题', value: 18, percentage: 25.7, cumulativePercentage: 61.4, improvement: '改进工艺流程，提升表面处理质量' },
    { name: '功能性问题', value: 15, percentage: 21.4, cumulativePercentage: 82.8, improvement: '完善测试验证，加强质量控制' },
    { name: '电气问题', value: 8, percentage: 11.4, cumulativePercentage: 94.2, improvement: '规范电气设计，加强元器件筛选' },
    { name: '其他问题', value: 4, percentage: 5.8, cumulativePercentage: 100.0, improvement: '持续监控，及时响应' }
  ],
  product: [
    { name: '发动机总成', value: 20, percentage: 40.0, cumulativePercentage: 40.0, improvement: '重点关注发动机质量控制' },
    { name: '车身外壳', value: 12, percentage: 24.0, cumulativePercentage: 64.0, improvement: '改进车身制造工艺' },
    { name: '空调系统', value: 10, percentage: 20.0, cumulativePercentage: 84.0, improvement: '优化空调系统设计' },
    { name: '刹车系统', value: 5, percentage: 10.0, cumulativePercentage: 94.0, improvement: '加强刹车系统测试' },
    { name: '变速箱', value: 3, percentage: 6.0, cumulativePercentage: 100.0, improvement: '持续改进变速箱质量' }
  ]
}

// 计算属性
const chartTitle = computed(() => {
  const typeMap = {
    defectType: '不良类型柏拉图分析',
    product: '产品型号柏拉图分析',
    customer: '客户柏拉图分析',
    rootCause: '根本原因柏拉图分析',
    factory: '工厂柏拉图分析'
  }
  return typeMap[configForm.value.analysisType] || '柏拉图分析'
})

const keyFactors = computed(() => {
  return paretoData.value.filter(item => item.cumulativePercentage <= 80)
})

const summary = computed(() => {
  const totalValue = paretoData.value.reduce((sum, item) => sum + item.value, 0)
  const keyFactorValue = keyFactors.value.reduce((sum, item) => sum + item.value, 0)
  
  return {
    totalCount: totalValue,
    itemCount: paretoData.value.length,
    keyFactorCount: keyFactors.value.length,
    keyFactorRatio: paretoData.value.length > 0 ? Math.round((keyFactors.value.length / paretoData.value.length) * 100) : 0,
    keyFactorContribution: totalValue > 0 ? Math.round((keyFactorValue / totalValue) * 100) : 0,
    improvementPotential: totalValue > 0 ? Math.round((keyFactorValue / totalValue) * 100) : 0
  }
})

// 柏拉图配置
const paretoOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: ['数量', '累计占比']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: paretoData.value.map(item => item.name)
  },
  yAxis: [
    {
      type: 'value',
      name: '数量',
      position: 'left'
    },
    {
      type: 'value',
      name: '累计占比(%)',
      position: 'right',
      max: 100
    }
  ],
  series: [
    {
      name: '数量',
      type: 'bar',
      data: paretoData.value.map(item => item.value),
      itemStyle: {
        color: '#409EFF'
      }
    },
    {
      name: '累计占比',
      type: 'line',
      yAxisIndex: 1,
      data: paretoData.value.map(item => item.cumulativePercentage),
      itemStyle: {
        color: '#F56C6C'
      },
      markLine: {
        data: [
          {
            yAxis: 80,
            lineStyle: {
              color: '#E6A23C',
              type: 'dashed'
            },
            label: {
              formatter: '80%线'
            }
          }
        ]
      }
    }
  ]
}))

// 历史对比图配置
const historyComparisonOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['本期', '上期']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['结构问题', '外观问题', '功能性问题', '电气问题', '其他问题']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '本期',
      type: 'bar',
      data: [25, 18, 15, 8, 4],
      itemStyle: {
        color: '#409EFF'
      }
    },
    {
      name: '上期',
      type: 'bar',
      data: [30, 20, 12, 10, 6],
      itemStyle: {
        color: '#909399'
      }
    }
  ]
}))

// 获取列标签
const getColumnLabel = (column: string) => {
  const metricMap = {
    count: { name: '项目名称', value: '投诉数量' },
    defectRate: { name: '项目名称', value: '不良率(%)' },
    cost: { name: '项目名称', value: '成本(元)' }
  }
  return metricMap[configForm.value.metric]?.[column] || column
}

// 格式化数值
const formatValue = (value: number) => {
  if (configForm.value.metric === 'cost') {
    return `¥${value.toLocaleString()}`
  } else if (configForm.value.metric === 'defectRate') {
    return `${value}%`
  }
  return value.toString()
}

// 获取行动建议
const getActionRecommendation = () => {
  const keyFactorCount = keyFactors.value.length
  const contribution = summary.value.keyFactorContribution
  
  if (keyFactorCount <= 2) {
    return `建议集中资源优先解决前${keyFactorCount}个关键问题，可获得${contribution}%的改善效果`
  } else {
    return `建议分阶段解决${keyFactorCount}个关键问题，第一阶段重点关注前2个问题`
  }
}

// 类型变化处理
const handleTypeChange = () => {
  loadData()
}

// 生成柏拉图
const handleGenerate = () => {
  loadData()
  ElMessage.success('柏拉图生成成功')
}

// 重置
const handleReset = () => {
  configForm.value = {
    analysisType: 'defectType',
    dateRange: [],
    metric: 'count'
  }
  loadData()
}

// 切换视图
const handleSwitchView = () => {
  showTable.value = !showTable.value
}

// 导出分析
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 加载数据
const loadData = () => {
  const data = mockData[configForm.value.analysisType] || mockData.defectType
  paretoData.value = [...data]
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.statistics-pareto {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.config-card {
  margin-bottom: 20px;
}

.charts-row {
  margin-bottom: 20px;
}

.analysis-row {
  margin-bottom: 20px;
}

.history-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  width: 100%;
}

.key-factors {
  padding: 10px 0;
}

.factor-summary ul {
  list-style: none;
  padding: 0;
}

.factor-summary li {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.factor-name {
  font-weight: 500;
}

.factor-value {
  color: #409eff;
  font-weight: bold;
}

.factor-percentage {
  color: #909399;
}

.improvement-priority {
  margin-top: 20px;
}

.improvement-priority h4 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.improvement-priority ol {
  padding-left: 20px;
}

.improvement-priority li {
  margin: 8px 0;
  line-height: 1.5;
}

.key-factor {
  color: #f56c6c;
  font-weight: bold;
}

.action-recommendations h4 {
  margin: 0 0 10px 0;
  color: #409eff;
}
</style>
