<template>
  <div class="analysis-classification">
    <div class="page-header">
      <h2>不良分类管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增分类
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon structure">
              <el-icon><Tools /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.structureCount }}</div>
              <div class="stats-label">结构问题</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon electrical">
              <el-icon><Lightning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.electricalCount }}</div>
              <div class="stats-label">电气问题</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon appearance">
              <el-icon><View /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.appearanceCount }}</div>
              <div class="stats-label">外观问题</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon functional">
              <el-icon><Setting /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.functionalCount }}</div>
              <div class="stats-label">功能性问题</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分类树形结构 -->
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>分类树</span>
              <el-button type="text" @click="expandAll">
                <el-icon><Expand /></el-icon>
                展开全部
              </el-button>
            </div>
          </template>
          <el-tree
            :data="treeData"
            :props="treeProps"
            node-key="id"
            ref="treeRef"
            @node-click="handleNodeClick"
            :expand-on-click-node="false"
            :default-expand-all="false"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span>{{ node.label }}</span>
                <span class="node-actions">
                  <el-button type="text" size="small" @click.stop="handleEditNode(data)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button type="text" size="small" @click.stop="handleDeleteNode(data)" v-if="data.level > 1">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </span>
              </span>
            </template>
          </el-tree>
        </el-card>
      </el-col>
      
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>分类详情</span>
              <el-button type="primary" @click="handleAddSubCategory" v-if="selectedCategory">
                <el-icon><Plus /></el-icon>
                添加子分类
              </el-button>
            </div>
          </template>
          
          <div v-if="selectedCategory" class="category-detail">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="分类名称">{{ selectedCategory.name }}</el-descriptions-item>
              <el-descriptions-item label="分类代码">{{ selectedCategory.code }}</el-descriptions-item>
              <el-descriptions-item label="分类级别">{{ selectedCategory.level }}</el-descriptions-item>
              <el-descriptions-item label="投诉数量">{{ selectedCategory.complaintCount }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ selectedCategory.createTime }}</el-descriptions-item>
              <el-descriptions-item label="更新时间">{{ selectedCategory.updateTime }}</el-descriptions-item>
              <el-descriptions-item label="分类描述" :span="2">{{ selectedCategory.description || '暂无描述' }}</el-descriptions-item>
            </el-descriptions>
            
            <!-- 相关投诉列表 -->
            <div class="related-complaints" v-if="selectedCategory.complaints && selectedCategory.complaints.length > 0">
              <h4>相关投诉</h4>
              <el-table :data="selectedCategory.complaints" size="small">
                <el-table-column prop="complaintNo" label="投诉编号" width="120" />
                <el-table-column prop="customerName" label="客户名称" width="140" />
                <el-table-column prop="productName" label="产品名称" width="120" />
                <el-table-column prop="complaintDate" label="投诉日期" width="110" />
                <el-table-column prop="status" label="状态" width="80">
                  <template #default="scope">
                    <el-tag size="small" :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="scope">
                    <el-button type="text" size="small" @click="viewComplaint(scope.row.complaintNo)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          
          <div v-else class="no-selection">
            <el-empty description="请选择左侧分类查看详情" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类代码" prop="code">
          <el-input v-model="form.code" placeholder="请输入分类代码" />
        </el-form-item>
        <el-form-item label="上级分类" prop="parentId" v-if="isAddSubCategory">
          <el-input v-model="form.parentName" disabled />
        </el-form-item>
        <el-form-item label="分类描述">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入分类描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// 统计数据
const stats = ref({
  structureCount: 15,
  electricalCount: 8,
  appearanceCount: 12,
  functionalCount: 10
})

// 树形数据
const treeRef = ref()
const treeProps = {
  children: 'children',
  label: 'name'
}

const treeData = ref([
  {
    id: 1,
    name: '结构问题',
    code: 'STRUCT',
    level: 1,
    complaintCount: 15,
    createTime: '2024-01-01',
    updateTime: '2024-01-15',
    description: '产品结构相关的质量问题',
    complaints: [
      { complaintNo: '*********', customerName: '北京汽车', productName: '发动机总成', complaintDate: '2024-01-15', status: '待处理' },
      { complaintNo: '*********', customerName: '深圳汽车', productName: '变速箱', complaintDate: '2024-01-12', status: '处理中' }
    ],
    children: [
      {
        id: 11,
        name: '机械磨损',
        code: 'STRUCT_WEAR',
        level: 2,
        complaintCount: 8,
        createTime: '2024-01-01',
        updateTime: '2024-01-15',
        description: '机械部件磨损导致的问题',
        complaints: [
          { complaintNo: '*********', customerName: '北京汽车', productName: '发动机总成', complaintDate: '2024-01-15', status: '待处理' }
        ]
      },
      {
        id: 12,
        name: '装配问题',
        code: 'STRUCT_ASSEMBLY',
        level: 2,
        complaintCount: 7,
        createTime: '2024-01-01',
        updateTime: '2024-01-15',
        description: '装配工艺导致的问题',
        complaints: [
          { complaintNo: '*********', customerName: '深圳汽车', productName: '变速箱', complaintDate: '2024-01-12', status: '处理中' }
        ]
      }
    ]
  },
  {
    id: 2,
    name: '电气问题',
    code: 'ELECTRICAL',
    level: 1,
    complaintCount: 8,
    createTime: '2024-01-01',
    updateTime: '2024-01-15',
    description: '电气系统相关的质量问题',
    complaints: [
      { complaintNo: 'CP2024003', customerName: '广州汽车', productName: '空调系统', complaintDate: '2024-01-13', status: '已解决' }
    ],
    children: [
      {
        id: 21,
        name: '控制电路',
        code: 'ELEC_CONTROL',
        level: 2,
        complaintCount: 5,
        createTime: '2024-01-01',
        updateTime: '2024-01-15',
        description: '控制电路相关问题',
        complaints: [
          { complaintNo: 'CP2024003', customerName: '广州汽车', productName: '空调系统', complaintDate: '2024-01-13', status: '已解决' }
        ]
      },
      {
        id: 22,
        name: '传感器故障',
        code: 'ELEC_SENSOR',
        level: 2,
        complaintCount: 3,
        createTime: '2024-01-01',
        updateTime: '2024-01-15',
        description: '传感器故障相关问题',
        complaints: []
      }
    ]
  },
  {
    id: 3,
    name: '外观问题',
    code: 'APPEARANCE',
    level: 1,
    complaintCount: 12,
    createTime: '2024-01-01',
    updateTime: '2024-01-15',
    description: '产品外观相关的质量问题',
    complaints: [
      { complaintNo: 'CP2024005', customerName: '天津汽车', productName: '车身外壳', complaintDate: '2024-01-11', status: '已关闭' }
    ],
    children: [
      {
        id: 31,
        name: '漆面问题',
        code: 'APP_PAINT',
        level: 2,
        complaintCount: 8,
        createTime: '2024-01-01',
        updateTime: '2024-01-15',
        description: '漆面质量问题',
        complaints: [
          { complaintNo: 'CP2024005', customerName: '天津汽车', productName: '车身外壳', complaintDate: '2024-01-11', status: '已关闭' }
        ]
      },
      {
        id: 32,
        name: '表面缺陷',
        code: 'APP_SURFACE',
        level: 2,
        complaintCount: 4,
        createTime: '2024-01-01',
        updateTime: '2024-01-15',
        description: '表面缺陷问题',
        complaints: []
      }
    ]
  },
  {
    id: 4,
    name: '功能性问题',
    code: 'FUNCTIONAL',
    level: 1,
    complaintCount: 10,
    createTime: '2024-01-01',
    updateTime: '2024-01-15',
    description: '产品功能相关的质量问题',
    complaints: [
      { complaintNo: 'CP2024002', customerName: '上海汽车', productName: '刹车系统', complaintDate: '2024-01-14', status: '处理中' }
    ],
    children: [
      {
        id: 41,
        name: '性能不达标',
        code: 'FUNC_PERFORMANCE',
        level: 2,
        complaintCount: 6,
        createTime: '2024-01-01',
        updateTime: '2024-01-15',
        description: '性能指标不达标',
        complaints: [
          { complaintNo: 'CP2024002', customerName: '上海汽车', productName: '刹车系统', complaintDate: '2024-01-14', status: '处理中' }
        ]
      },
      {
        id: 42,
        name: '功能失效',
        code: 'FUNC_FAILURE',
        level: 2,
        complaintCount: 4,
        createTime: '2024-01-01',
        updateTime: '2024-01-15',
        description: '功能完全失效',
        complaints: []
      }
    ]
  }
])

// 选中的分类
const selectedCategory = ref(null)

// 对话框
const dialogVisible = ref(false)
const dialogTitle = computed(() => isEdit.value ? '编辑分类' : '新增分类')
const isEdit = ref(false)
const isAddSubCategory = ref(false)

// 表单
const formRef = ref<FormInstance>()
const form = ref({
  id: '',
  name: '',
  code: '',
  parentId: '',
  parentName: '',
  description: ''
})

const rules: FormRules = {
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入分类代码', trigger: 'blur' }]
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '待处理': return 'danger'
    case '处理中': return 'warning'
    case '已解决': return 'success'
    case '已关闭': return 'info'
    default: return ''
  }
}

// 展开全部
const expandAll = () => {
  const nodes = treeRef.value.store._getAllNodes()
  nodes.forEach((node: any) => {
    node.expanded = true
  })
}

// 节点点击
const handleNodeClick = (data: any) => {
  selectedCategory.value = data
}

// 新增分类
const handleAdd = () => {
  isEdit.value = false
  isAddSubCategory.value = false
  form.value = {
    id: '',
    name: '',
    code: '',
    parentId: '',
    parentName: '',
    description: ''
  }
  dialogVisible.value = true
}

// 添加子分类
const handleAddSubCategory = () => {
  if (!selectedCategory.value) return
  
  isEdit.value = false
  isAddSubCategory.value = true
  form.value = {
    id: '',
    name: '',
    code: '',
    parentId: selectedCategory.value.id,
    parentName: selectedCategory.value.name,
    description: ''
  }
  dialogVisible.value = true
}

// 编辑节点
const handleEditNode = (data: any) => {
  isEdit.value = true
  isAddSubCategory.value = false
  form.value = {
    id: data.id,
    name: data.name,
    code: data.code,
    parentId: '',
    parentName: '',
    description: data.description || ''
  }
  dialogVisible.value = true
}

// 删除节点
const handleDeleteNode = (data: any) => {
  ElMessageBox.confirm(`确认删除分类"${data.name}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
    // 这里应该调用删除API
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    ElMessage.success(isEdit.value ? '更新成功' : '添加成功')
    dialogVisible.value = false
    // 这里应该调用API并刷新数据
  } catch (error) {
    ElMessage.error('请检查表单填写是否完整')
  }
}

// 查看投诉
const viewComplaint = (complaintNo: string) => {
  ElMessage.info(`查看投诉 ${complaintNo}`)
}

onMounted(() => {
  // 默认选中第一个节点
  if (treeData.value.length > 0) {
    selectedCategory.value = treeData.value[0]
  }
})
</script>

<style scoped>
.analysis-classification {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stats-icon.structure {
  background-color: #f56c6c;
}

.stats-icon.electrical {
  background-color: #e6a23c;
}

.stats-icon.appearance {
  background-color: #409eff;
}

.stats-icon.functional {
  background-color: #67c23a;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.node-actions {
  display: none;
}

.custom-tree-node:hover .node-actions {
  display: block;
}

.category-detail {
  padding: 20px 0;
}

.related-complaints {
  margin-top: 20px;
}

.related-complaints h4 {
  margin: 0 0 15px 0;
  color: #409eff;
  font-size: 16px;
}

.no-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>
