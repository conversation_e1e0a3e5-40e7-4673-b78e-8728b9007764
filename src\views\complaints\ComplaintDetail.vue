<template>
  <div class="complaint-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="goBack" class="back-btn">
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>投诉详情</h2>
      <div class="header-actions">
        <el-button type="warning" @click="handleEdit">编辑</el-button>
        <el-button type="success" @click="handleAnalysis">分析</el-button>
        <el-button type="danger" @click="handle8D">8D</el-button>
      </div>
    </div>

    <!-- 基本信息 -->
    <el-card class="info-card" v-if="complaintData">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-tag :type="getStatusType(complaintData.status)">{{ complaintData.status }}</el-tag>
        </div>
      </template>
      
      <el-descriptions :column="3" border>
        <el-descriptions-item label="投诉编号">{{ complaintData.complaintNo }}</el-descriptions-item>
        <el-descriptions-item label="投诉日期">{{ complaintData.complaintDate }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ complaintData.customerName }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{ complaintData.contactInfo }}</el-descriptions-item>
        <el-descriptions-item label="客户地址" :span="2">{{ complaintData.customerAddress }}</el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ complaintData.productName }}</el-descriptions-item>
        <el-descriptions-item label="产品型号">{{ complaintData.productModel }}</el-descriptions-item>
        <el-descriptions-item label="生产批次">{{ complaintData.productionBatch }}</el-descriptions-item>
        <el-descriptions-item label="生产日期">{{ complaintData.productionDate }}</el-descriptions-item>
        <el-descriptions-item label="客户车型">{{ complaintData.customerVehicleType }}</el-descriptions-item>
        <el-descriptions-item label="工厂车型">{{ complaintData.factoryVehicleType }}</el-descriptions-item>
        <el-descriptions-item label="不良数量">{{ complaintData.defectQuantity }}</el-descriptions-item>
        <el-descriptions-item label="不良率">{{ complaintData.defectRate }}%</el-descriptions-item>
        <el-descriptions-item label="不良阶段">{{ complaintData.defectStage }}</el-descriptions-item>
        <el-descriptions-item label="严重程度">
          <el-tag :type="getSeverityType(complaintData.severity)">{{ complaintData.severity }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="不良描述" :span="3">{{ complaintData.defectDescription }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 围堵措施 -->
    <el-card class="containment-card">
      <template #header>
        <div class="card-header">
          <span>围堵措施</span>
          <el-button type="primary" size="small" @click="handleContainment">
            <el-icon><Plus /></el-icon>
            管理围堵措施
          </el-button>
        </div>
      </template>
      
      <el-table :data="containmentMeasures" border style="width: 100%">
        <el-table-column prop="location" label="地点" width="120" />
        <el-table-column prop="quantity" label="数量" width="80" />
        <el-table-column prop="responsible" label="责任人" width="100" />
        <el-table-column prop="department" label="部门" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getContainmentStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="measures" label="围堵措施" min-width="200" />
        <el-table-column prop="expectedTime" label="预计完成时间" width="120" />
        <el-table-column prop="result" label="围堵结果" min-width="150" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button type="primary" size="small" @click="editContainment(scope.row, scope.$index)">编辑</el-button>
            <el-button type="danger" size="small" @click="deleteContainment(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div v-if="containmentMeasures.length === 0" class="empty-state">
        <el-empty description="暂无围堵措施" />
      </div>
    </el-card>

    <!-- 围堵措施管理对话框 -->
    <el-dialog v-model="containmentDialogVisible" title="围堵措施管理" width="900px">
      <div class="containment-header">
        <div class="header-info">
          <span>投诉编号：{{ complaintData?.complaintNo }}</span>
        </div>
        <el-button type="primary" size="small" @click="handleAddContainment">
          <el-icon><Plus /></el-icon>
          新增围堵
        </el-button>
      </div>

      <!-- 围堵措施列表 -->
      <div class="containment-list">
        <div v-for="(item, index) in editingContainmentList" :key="index" class="containment-item">
          <div class="item-header">
            <span class="item-number">No{{ index + 1 }}</span>
            <el-button type="text" size="small" @click="handleRemoveContainment(index)" class="remove-btn">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          
          <el-row :gutter="16" class="item-content">
            <el-col :span="6">
              <div class="form-item">
                <label>地点</label>
                <el-select v-model="item.location" placeholder="仓库" style="width: 100%">
                  <el-option label="仓库" value="仓库" />
                  <el-option label="生产线" value="生产线" />
                  <el-option label="客户现场" value="客户现场" />
                  <el-option label="运输途中" value="运输途中" />
                  <el-option label="供应商" value="供应商" />
                </el-select>
              </div>
            </el-col>
            
            <el-col :span="4">
              <div class="form-item">
                <label>数量</label>
                <el-input-number v-model="item.quantity" :min="0" style="width: 100%" />
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="form-item">
                <label>责任人</label>
                <el-select v-model="item.responsible" placeholder="张三" style="width: 100%">
                  <el-option label="张三" value="张三" />
                  <el-option label="李四" value="李四" />
                  <el-option label="王五" value="王五" />
                  <el-option label="赵六" value="赵六" />
                </el-select>
              </div>
            </el-col>
            
            <el-col :span="4">
              <div class="form-item">
                <label>部门</label>
                <el-input v-model="item.department" placeholder="PE" />
              </div>
            </el-col>
            
            <el-col :span="4">
              <div class="form-item">
                <label>状态</label>
                <el-select v-model="item.status" placeholder="进展情况" style="width: 100%">
                  <el-option label="计划中" value="计划中" />
                  <el-option label="执行中" value="执行中" />
                  <el-option label="已完成" value="已完成" />
                  <el-option label="已暂停" value="已暂停" />
                </el-select>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" class="item-content">
            <el-col :span="12">
              <div class="form-item">
                <label>围堵措施</label>
                <el-input v-model="item.measures" placeholder="请输入具体的围堵措施" />
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="form-item">
                <label>预计完成时间</label>
                <el-date-picker
                  v-model="item.expectedTime"
                  type="date"
                  placeholder="请选择日期"
                  style="width: 100%"
                />
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="form-item">
                <label>围堵结果</label>
                <el-input v-model="item.result" placeholder="围堵结果" />
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="dialog-actions">
        <el-button type="primary" @click="handleAddResponsible">添加责任人</el-button>
        <el-button type="success" @click="handleSubmitContainment">提交申请</el-button>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="containmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleContainmentSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Close } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 投诉数据
const complaintData = ref(null)
const containmentDialogVisible = ref(false)

// 围堵措施数据
const containmentMeasures = ref([
  {
    location: '仓库',
    quantity: 100,
    responsible: '张三',
    department: 'PE',
    status: '已完成',
    measures: '立即停止该批次产品发货，对库存产品进行全面检查',
    expectedTime: '2024-01-20',
    result: '已完成100%检查，发现3台同批次产品存在类似问题'
  },
  {
    location: '生产线',
    quantity: 50,
    responsible: '李四',
    department: '生产',
    status: '执行中',
    measures: '暂停生产线，对所有在制品进行检验',
    expectedTime: '2024-01-22',
    result: '正在执行中'
  }
])

const editingContainmentList = ref([])

// 返回列表
const goBack = () => {
  router.push('/complaints/list')
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '待处理': return 'warning'
    case '处理中': return 'primary'
    case '已解决': return 'success'
    case '已关闭': return 'info'
    default: return ''
  }
}

// 获取严重程度类型
const getSeverityType = (severity: string) => {
  switch (severity) {
    case '紧急': return 'danger'
    case '严重': return 'warning'
    case '一般': return 'primary'
    case '轻微': return 'info'
    default: return ''
  }
}

// 获取围堵措施状态类型
const getContainmentStatusType = (status: string) => {
  switch (status) {
    case '计划中': return 'info'
    case '执行中': return 'warning'
    case '已完成': return 'success'
    case '已暂停': return 'danger'
    default: return ''
  }
}

// 处理方法
const handleEdit = () => {
  ElMessage.info('编辑功能开发中...')
}

const handleAnalysis = () => {
  ElMessage.info('跳转到原因分析页面...')
}

const handle8D = () => {
  ElMessage.info(`启动8D流程处理投诉：${complaintData.value?.complaintNo}`)
}

const handleContainment = () => {
  editingContainmentList.value = JSON.parse(JSON.stringify(containmentMeasures.value))
  containmentDialogVisible.value = true
}

const handleAddContainment = () => {
  editingContainmentList.value.push({
    location: '',
    quantity: 0,
    responsible: '',
    department: '',
    status: '计划中',
    measures: '',
    expectedTime: '',
    result: ''
  })
}

const handleRemoveContainment = (index: number) => {
  if (editingContainmentList.value.length > 1) {
    editingContainmentList.value.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一条围堵措施')
  }
}

const handleAddResponsible = () => {
  ElMessage.info('添加责任人功能开发中...')
}

const handleSubmitContainment = () => {
  ElMessage.success('围堵措施申请已提交')
}

const handleContainmentSubmit = () => {
  const hasEmptyFields = editingContainmentList.value.some(item => 
    !item.location || !item.responsible || !item.measures
  )
  
  if (hasEmptyFields) {
    ElMessage.error('请完善围堵措施信息')
    return
  }
  
  containmentMeasures.value = JSON.parse(JSON.stringify(editingContainmentList.value))
  ElMessage.success('围堵措施保存成功')
  containmentDialogVisible.value = false
}

const editContainment = (row: any, index: number) => {
  ElMessage.info(`编辑第${index + 1}条围堵措施`)
}

const deleteContainment = (index: number) => {
  ElMessageBox.confirm('确认删除此条围堵措施吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    containmentMeasures.value.splice(index, 1)
    ElMessage.success('删除成功')
  })
}

// 加载数据
const loadData = () => {
  const id = route.params.id
  // 这里应该根据ID从API获取数据，现在使用模拟数据
  complaintData.value = {
    id: 1,
    complaintNo: 'CP2024001',
    complaintDate: '2024-01-15',
    customerName: '北京汽车制造有限公司',
    contactInfo: '13800138001',
    customerAddress: '北京市朝阳区汽车产业园区A座',
    productName: '发动机总成',
    productModel: 'Model A',
    productionBatch: 'B20240101',
    productionDate: '2024-01-10',
    customerVehicleType: '轿车',
    factoryVehicleType: 'A型车',
    defectDescription: '发动机启动时出现异响，影响正常使用',
    defectQuantity: 5,
    defectRate: 2.5,
    defectStage: '使用',
    severity: '严重',
    status: '待处理'
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.complaint-detail-page {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.page-header h2 {
  margin: 0;
  flex: 1;
  text-align: center;
}

.back-btn {
  margin-right: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.info-card, .containment-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

/* 围堵措施样式 */
.containment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.containment-list {
  max-height: 500px;
  overflow-y: auto;
}

.containment-item {
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-number {
  font-weight: 500;
  color: #409eff;
}

.remove-btn {
  color: #f56c6c;
}

.item-content {
  margin-bottom: 12px;
}

.form-item {
  margin-bottom: 8px;
}

.form-item label {
  display: block;
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.dialog-actions {
  margin: 20px 0;
  text-align: center;
}

.dialog-actions .el-button {
  margin: 0 8px;
}
</style>
