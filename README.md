# 客户投诉质量管理系统原型

## 项目概述

本项目是为汽车制造工厂设计的售后质量管理系统原型，核心在于高效处理客户投诉、追踪返工数据，并进行深入的原因分析和多维度统计。

## 系统架构

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **图表库**: ECharts + Vue-ECharts
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **构建工具**: Vite

## 功能模块

### 1. 客户投诉管理模块
- 投诉信息录入：完整的投诉信息录入表单，包括投诉编号自动生成、客户信息、产品信息等
- 投诉状态管理：跟踪投诉处理进度（待处理、处理中、已解决、已关闭）
- 附件上传：支持上传图片、视频、文档等证据文件
- 历史查询：多维度搜索功能，支持按日期、客户、产品等条件查询

### 2. 客户返工数据集成模块
- 数据接口配置：与MES/WMS系统集成，自动拉取返工数据
- 返工类型管理：客户返工、我方挑选、第三方挑选、换货等类型区分
- 返工成本追踪：详细记录运输费、人工费、物料费、检测费等各项成本
- 数据关联：返工数据与投诉记录自动关联匹配

### 3. 客户投诉原因分析与对策模块
- 不良分类：标准化分类管理（结构问题、电气问题、外观问题、功能性问题）
- 临时对策录入：记录立即缓解问题的临时措施
- 根本原因分析：支持5Why分析法、鱼骨图等分析工具
- 改善对策制定：制定长期有效的改善措施，指定责任部门和责任人
- 对策执行追踪：跟踪对策执行状态和完成情况

### 4. 统计分析与报表模块
- 多维度统计分析：按日期、客户、产品、型号、工厂、不良类型等维度分析
- 投诉趋势分析：投诉量趋势图、不良率趋势图，识别投诉高峰期
- TOP问题柏拉图：不良类型、产品型号、根本原因柏拉图分析
- 报表生成：客户投诉周报/月报、产品质量分析报告、返工成本分析报告
- 图表可视化：柱状图、折线图、饼图、柏拉图等多种图表展示

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖
```sh
npm install
```

### 开发环境运行
```sh
npm run dev
```
访问 http://localhost:3000

### 构建生产版本
```sh
npm run build
```

### 预览生产版本
```sh
npm run preview
```

## 项目结构

```
customer-complaints/
├── public/                 # 静态资源
├── src/
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   │   └── Layout.vue     # 布局组件
│   ├── router/            # 路由配置
│   │   └── index.ts
│   ├── stores/            # 状态管理
│   ├── views/             # 页面组件
│   │   ├── Dashboard.vue  # 首页
│   │   ├── complaints/    # 客户投诉管理
│   │   │   ├── ComplaintsList.vue
│   │   │   └── ComplaintsAdd.vue
│   │   ├── rework/        # 返工数据管理
│   │   │   ├── ReworkList.vue
│   │   │   └── ReworkCost.vue
│   │   ├── analysis/      # 原因分析对策
│   │   │   ├── AnalysisList.vue
│   │   │   └── AnalysisClassification.vue
│   │   └── statistics/    # 统计分析报表
│   │       ├── StatisticsDashboard.vue
│   │       ├── StatisticsTrends.vue
│   │       └── StatisticsPareto.vue
│   ├── App.vue           # 根组件
│   └── main.ts           # 入口文件
├── demo.html             # 静态演示页面
├── package.json
├── vite.config.ts
└── README.md
```

## 技术特点

1. **响应式设计**：适配不同屏幕尺寸，操作列按钮自适应布局
2. **模块化架构**：清晰的功能模块划分，易于维护和扩展
3. **数据可视化**：丰富的图表展示，支持ECharts图表库
4. **用户体验**：直观的操作界面，Element Plus组件库
5. **示例数据**：每个页面配置5条示例数据，便于演示

## 示例数据说明

系统中包含了完整的示例数据：
- **投诉记录**：5条不同类型的投诉记录，涵盖各种严重度和状态
- **返工数据**：5条返工记录，包含不同返工类型和成本信息
- **分析记录**：5条原因分析记录，展示完整的分析流程
- **不良分类**：完整的树形分类结构，支持二级分类
- **统计图表**：趋势分析、柏拉图等可视化数据

## 注意事项

1. **原型演示**：本项目为原型演示，不包含后台API实现
2. **模拟数据**：所有数据均为前端模拟数据，用于界面展示
3. **功能演示**：文件上传、数据导出等功能为界面演示
4. **兼容性**：已解决Node.js 18版本兼容性问题
5. **部署准备**：实际部署时需要配置相应的后台服务

## 演示地址

- **开发环境**：http://localhost:3000
- **静态演示**：直接打开 `demo.html` 文件

## 联系方式

如有问题或建议，请联系开发团队。
