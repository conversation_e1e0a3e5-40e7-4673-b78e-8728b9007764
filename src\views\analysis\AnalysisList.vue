<template>
  <div class="analysis-list">
    <div class="page-header">
      <h2>投诉原因分析与对策</h2>
      <el-button type="primary" @click="handleAddAnalysis">
        <el-icon><Plus /></el-icon>
        新增分析
      </el-button>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="投诉编号">
          <el-input v-model="searchForm.complaintNo" placeholder="请输入投诉编号" clearable />
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model="searchForm.customerName" placeholder="请输入客户名称" clearable />
        </el-form-item>
        <el-form-item label="不良分类">
          <el-select v-model="searchForm.defectCategory" placeholder="请选择不良分类" clearable>
            <el-option label="结构问题" value="结构问题" />
            <el-option label="电气问题" value="电气问题" />
            <el-option label="外观问题" value="外观问题" />
            <el-option label="功能性问题" value="功能性问题" />
          </el-select>
        </el-form-item>
        <el-form-item label="分析状态">
          <el-select v-model="searchForm.analysisStatus" placeholder="请选择分析状态" clearable>
            <el-option label="待分析" value="待分析" />
            <el-option label="分析中" value="分析中" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已验证" value="已验证" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 分析列表 -->
    <el-card class="table-card">
      <el-table :data="analysisData" style="width: 100%" v-loading="loading">
        <el-table-column prop="analysisNo" label="分析编号" width="120" />
        <el-table-column prop="complaintNo" label="投诉编号" width="120">
          <template #default="scope">
            <el-link type="primary" @click="viewComplaint(scope.row.complaintNo)">
              {{ scope.row.complaintNo }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="客户名称" width="140" />
        <el-table-column prop="productName" label="产品名称" width="120" />
        <el-table-column prop="defectCategory" label="不良分类" width="100">
          <template #default="scope">
            <el-tag :type="getDefectCategoryColor(scope.row.defectCategory)">{{ scope.row.defectCategory }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="rootCause" label="根本原因" min-width="150" show-overflow-tooltip />
        <el-table-column prop="responsibleDept" label="责任部门" width="100" />
        <el-table-column prop="responsiblePerson" label="责任人" width="100" />
        <el-table-column prop="analysisStatus" label="分析状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.analysisStatus)">{{ scope.row.analysisStatus }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="analysisDate" label="分析日期" width="110" />
        <el-table-column label="操作" width="260" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleView(scope.row)">查看</el-button>
              <el-button type="warning" size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="success" size="small" @click="handleCountermeasure(scope.row)">对策</el-button>
              <el-button type="info" size="small" @click="handleTrack(scope.row)">追踪</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="分析详情" width="900px">
      <div v-if="currentAnalysis" class="analysis-detail">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="分析编号">{{ currentAnalysis.analysisNo }}</el-descriptions-item>
              <el-descriptions-item label="投诉编号">{{ currentAnalysis.complaintNo }}</el-descriptions-item>
              <el-descriptions-item label="客户名称">{{ currentAnalysis.customerName }}</el-descriptions-item>
              <el-descriptions-item label="产品名称">{{ currentAnalysis.productName }}</el-descriptions-item>
              <el-descriptions-item label="不良分类">
                <el-tag :type="getDefectCategoryColor(currentAnalysis.defectCategory)">{{ currentAnalysis.defectCategory }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="分析状态">
                <el-tag :type="getStatusColor(currentAnalysis.analysisStatus)">{{ currentAnalysis.analysisStatus }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="责任部门">{{ currentAnalysis.responsibleDept }}</el-descriptions-item>
              <el-descriptions-item label="责任人">{{ currentAnalysis.responsiblePerson }}</el-descriptions-item>
              <el-descriptions-item label="分析日期">{{ currentAnalysis.analysisDate }}</el-descriptions-item>
              <el-descriptions-item label="完成日期">{{ currentAnalysis.completionDate || '-' }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          
          <el-tab-pane label="原因分析" name="analysis">
            <div class="analysis-content">
              <h4>临时对策</h4>
              <p>{{ currentAnalysis.temporaryMeasure || '暂无' }}</p>
              
              <h4>根本原因分析</h4>
              <p>{{ currentAnalysis.rootCause || '暂无' }}</p>
              
              <h4>5Why分析</h4>
              <div v-if="currentAnalysis.whyAnalysis && currentAnalysis.whyAnalysis.length > 0">
                <div v-for="(why, index) in currentAnalysis.whyAnalysis" :key="index" class="why-item">
                  <strong>Why {{ index + 1 }}:</strong> {{ why }}
                </div>
              </div>
              <p v-else>暂无5Why分析</p>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="改善对策" name="countermeasure">
            <div class="countermeasure-content">
              <h4>改善对策</h4>
              <p>{{ currentAnalysis.improvementMeasure || '暂无' }}</p>
              
              <h4>执行状态</h4>
              <el-tag :type="getExecutionStatusColor(currentAnalysis.executionStatus)">
                {{ currentAnalysis.executionStatus || '未开始' }}
              </el-tag>
              
              <h4>预期完成时间</h4>
              <p>{{ currentAnalysis.expectedCompletionDate || '暂无' }}</p>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="费用信息" name="cost">
            <el-table :data="[currentAnalysis]" border>
              <el-table-column prop="detectionCost" label="检测费" width="100">
                <template #default="scope">¥{{ scope.row.detectionCost?.toLocaleString() || 0 }}</template>
              </el-table-column>
              <el-table-column prop="repairCost" label="维修费" width="100">
                <template #default="scope">¥{{ scope.row.repairCost?.toLocaleString() || 0 }}</template>
              </el-table-column>
              <el-table-column prop="materialCost" label="物料费" width="100">
                <template #default="scope">¥{{ scope.row.materialCost?.toLocaleString() || 0 }}</template>
              </el-table-column>
              <el-table-column prop="transportCost" label="运输费" width="100">
                <template #default="scope">¥{{ scope.row.transportCost?.toLocaleString() || 0 }}</template>
              </el-table-column>
              <el-table-column prop="laborCost" label="人工费" width="100">
                <template #default="scope">¥{{ scope.row.laborCost?.toLocaleString() || 0 }}</template>
              </el-table-column>
              <el-table-column prop="totalCost" label="总费用" width="100">
                <template #default="scope">
                  <strong style="color: #f56c6c;">¥{{ scope.row.totalCost?.toLocaleString() || 0 }}</strong>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 搜索表单
const searchForm = ref({
  complaintNo: '',
  customerName: '',
  defectCategory: '',
  analysisStatus: ''
})

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const analysisData = ref([])
const loading = ref(false)

// 对话框
const viewDialogVisible = ref(false)
const currentAnalysis = ref(null)
const activeTab = ref('basic')

// 示例数据
const mockData = [
  {
    id: 1,
    analysisNo: 'AN2024001',
    complaintNo: 'CP2024001',
    customerName: '北京汽车制造有限公司',
    productName: '发动机总成',
    defectCategory: '结构问题',
    rootCause: '轴承磨损导致异响，原因为润滑油品质不达标',
    responsibleDept: '质量部',
    responsiblePerson: '张工程师',
    analysisStatus: '已完成',
    analysisDate: '2024-01-16',
    completionDate: '2024-01-18',
    temporaryMeasure: '立即更换异响发动机，提供临时替代品',
    whyAnalysis: [
      '发动机出现异响',
      '轴承磨损严重',
      '润滑不足',
      '润滑油品质不达标',
      '供应商质量控制不严'
    ],
    improvementMeasure: '1.更换润滑油供应商 2.加强进料检验 3.完善质量控制流程',
    executionStatus: '执行中',
    expectedCompletionDate: '2024-02-15',
    detectionCost: 2000,
    repairCost: 8000,
    materialCost: 10000,
    transportCost: 5000,
    laborCost: 600,
    totalCost: 25600
  },
  {
    id: 2,
    analysisNo: 'AN2024002',
    complaintNo: 'CP2024002',
    customerName: '上海汽车工业集团',
    productName: '刹车系统',
    defectCategory: '功能性问题',
    rootCause: '刹车片材料配方问题导致制动距离过长',
    responsibleDept: '研发部',
    responsiblePerson: '李工程师',
    analysisStatus: '分析中',
    analysisDate: '2024-01-15',
    completionDate: '',
    temporaryMeasure: '调整刹车片压力，临时改善制动效果',
    whyAnalysis: [
      '制动距离过长',
      '刹车片摩擦力不足',
      '材料配方不当',
      '配方验证不充分',
      '研发流程存在缺陷'
    ],
    improvementMeasure: '重新设计刹车片配方，加强验证测试',
    executionStatus: '计划中',
    expectedCompletionDate: '2024-02-28',
    detectionCost: 500,
    repairCost: 6000,
    materialCost: 2000,
    transportCost: 0,
    laborCost: 0,
    totalCost: 8500
  },
  {
    id: 3,
    analysisNo: 'AN2024003',
    complaintNo: 'CP2024003',
    customerName: '广州汽车集团',
    productName: '空调系统',
    defectCategory: '电气问题',
    rootCause: '压缩机控制电路设计缺陷',
    responsibleDept: '电气部',
    responsiblePerson: '王工程师',
    analysisStatus: '已验证',
    analysisDate: '2024-01-14',
    completionDate: '2024-01-20',
    temporaryMeasure: '手动调节温度控制，绕过自动控制电路',
    whyAnalysis: [
      '空调制冷效果差',
      '压缩机工作不稳定',
      '控制信号异常',
      '电路设计存在缺陷',
      '设计评审不充分'
    ],
    improvementMeasure: '重新设计控制电路，增加冗余保护',
    executionStatus: '已完成',
    expectedCompletionDate: '2024-01-25',
    detectionCost: 1000,
    repairCost: 0,
    materialCost: 0,
    transportCost: 1000,
    laborCost: 10000,
    totalCost: 12000
  },
  {
    id: 4,
    analysisNo: 'AN2024004',
    complaintNo: 'CP2024004',
    customerName: '深圳汽车技术有限公司',
    productName: '变速箱',
    defectCategory: '结构问题',
    rootCause: '齿轮加工精度不足导致顿挫',
    responsibleDept: '制造部',
    responsiblePerson: '赵工程师',
    analysisStatus: '待分析',
    analysisDate: '2024-01-13',
    completionDate: '',
    temporaryMeasure: '调整换挡逻辑，减少顿挫感',
    whyAnalysis: [],
    improvementMeasure: '',
    executionStatus: '未开始',
    expectedCompletionDate: '',
    detectionCost: 0,
    repairCost: 0,
    materialCost: 0,
    transportCost: 3200,
    laborCost: 0,
    totalCost: 3200
  },
  {
    id: 5,
    analysisNo: 'AN2024005',
    complaintNo: 'CP2024005',
    customerName: '天津汽车制造厂',
    productName: '车身外壳',
    defectCategory: '外观问题',
    rootCause: '喷漆工艺参数设置不当',
    responsibleDept: '工艺部',
    responsiblePerson: '孙工程师',
    analysisStatus: '已完成',
    analysisDate: '2024-01-12',
    completionDate: '2024-01-15',
    temporaryMeasure: '重新喷漆处理',
    whyAnalysis: [
      '车身漆面色差',
      '喷漆厚度不均',
      '工艺参数不当',
      '设备校准不准确',
      '维护保养不及时'
    ],
    improvementMeasure: '重新校准设备，制定标准作业指导书',
    executionStatus: '已完成',
    expectedCompletionDate: '2024-01-20',
    detectionCost: 800,
    repairCost: 8000,
    materialCost: 5000,
    transportCost: 2000,
    laborCost: 0,
    totalCost: 15800
  }
]

// 获取不良分类颜色
const getDefectCategoryColor = (category: string) => {
  switch (category) {
    case '结构问题': return 'danger'
    case '电气问题': return 'warning'
    case '外观问题': return 'info'
    case '功能性问题': return 'success'
    default: return ''
  }
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case '待分析': return 'info'
    case '分析中': return 'warning'
    case '已完成': return 'success'
    case '已验证': return 'success'
    default: return ''
  }
}

// 获取执行状态颜色
const getExecutionStatusColor = (status: string) => {
  switch (status) {
    case '未开始': return 'info'
    case '计划中': return 'warning'
    case '执行中': return 'primary'
    case '已完成': return 'success'
    default: return 'info'
  }
}

// 新增分析
const handleAddAnalysis = () => {
  ElMessage.info('新增分析功能开发中...')
}

// 搜索
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loadData()
    loading.value = false
  }, 500)
}

// 重置
const handleReset = () => {
  searchForm.value = {
    complaintNo: '',
    customerName: '',
    defectCategory: '',
    analysisStatus: ''
  }
  loadData()
}

// 查看详情
const handleView = (row: any) => {
  currentAnalysis.value = row
  activeTab.value = 'basic'
  viewDialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  ElMessage.info('编辑功能开发中...')
}

// 对策
const handleCountermeasure = (row: any) => {
  ElMessage.info('对策管理功能开发中...')
}

// 追踪
const handleTrack = (row: any) => {
  ElMessage.info('执行追踪功能开发中...')
}

// 查看投诉
const viewComplaint = (complaintNo: string) => {
  ElMessage.info(`查看投诉 ${complaintNo}`)
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val
  loadData()
}

// 加载数据
const loadData = () => {
  analysisData.value = mockData
  pagination.value.total = mockData.length
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.analysis-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.analysis-detail {
  padding: 20px 0;
}

.analysis-content h4,
.countermeasure-content h4 {
  margin: 15px 0 10px 0;
  color: #409eff;
  font-size: 14px;
}

.analysis-content p,
.countermeasure-content p {
  margin: 0 0 15px 0;
  line-height: 1.6;
}

.why-item {
  margin: 8px 0;
  padding: 8px;
  background-color: #f5f7fa;
  border-left: 3px solid #409eff;
}
</style>
