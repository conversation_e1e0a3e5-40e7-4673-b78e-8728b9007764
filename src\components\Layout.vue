<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside width="250px" class="sidebar">
      <div class="logo">
        <h2>客户投诉质量管理系统</h2>
      </div>
      <el-menu
        :default-active="$route.path"
        class="el-menu-vertical"
        router
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
      >
        <el-menu-item index="/dashboard">
          <el-icon><House /></el-icon>
          <span>首页</span>
        </el-menu-item>
        
        <el-sub-menu index="complaints">
          <template #title>
            <el-icon><Document /></el-icon>
            <span>客户投诉管理</span>
          </template>
          <el-menu-item index="/complaints/list">投诉列表</el-menu-item>
          <el-menu-item index="/complaints/add">新增投诉</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="rework">
          <template #title>
            <el-icon><Tools /></el-icon>
            <span>返工数据管理</span>
          </template>
          <el-menu-item index="/rework/list">返工数据</el-menu-item>
          <el-menu-item index="/rework/cost">成本追踪</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="analysis">
          <template #title>
            <el-icon><DataAnalysis /></el-icon>
            <span>原因分析对策</span>
          </template>
          <el-menu-item index="/analysis/list">分析列表</el-menu-item>
          <el-menu-item index="/analysis/classification">不良分类</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="statistics">
          <template #title>
            <el-icon><TrendCharts /></el-icon>
            <span>统计分析报表</span>
          </template>
          <el-menu-item index="/statistics/dashboard">数据看板</el-menu-item>
          <el-menu-item index="/statistics/trends">趋势分析</el-menu-item>
          <el-menu-item index="/statistics/pareto">柏拉图分析</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="breadcrumbItems.length > 0" v-for="item in breadcrumbItems" :key="item.path" :to="{ path: item.path }">
              {{ item.name }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <el-dropdown>
            <span class="el-dropdown-link">
              <el-icon><User /></el-icon>
              管理员
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人设置</el-dropdown-item>
                <el-dropdown-item divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// 面包屑导航
const breadcrumbItems = computed(() => {
  const path = route.path
  const items: Array<{ name: string; path: string }> = []
  
  if (path.startsWith('/complaints')) {
    items.push({ name: '客户投诉管理', path: '/complaints' })
    if (path === '/complaints/list') items.push({ name: '投诉列表', path: '/complaints/list' })
    if (path === '/complaints/add') items.push({ name: '新增投诉', path: '/complaints/add' })
  } else if (path.startsWith('/rework')) {
    items.push({ name: '返工数据管理', path: '/rework' })
    if (path === '/rework/list') items.push({ name: '返工数据', path: '/rework/list' })
    if (path === '/rework/cost') items.push({ name: '成本追踪', path: '/rework/cost' })
  } else if (path.startsWith('/analysis')) {
    items.push({ name: '原因分析对策', path: '/analysis' })
    if (path === '/analysis/list') items.push({ name: '分析列表', path: '/analysis/list' })
    if (path === '/analysis/classification') items.push({ name: '不良分类', path: '/analysis/classification' })
  } else if (path.startsWith('/statistics')) {
    items.push({ name: '统计分析报表', path: '/statistics' })
    if (path === '/statistics/dashboard') items.push({ name: '数据看板', path: '/statistics/dashboard' })
    if (path === '/statistics/trends') items.push({ name: '趋势分析', path: '/statistics/trends' })
    if (path === '/statistics/pareto') items.push({ name: '柏拉图分析', path: '/statistics/pareto' })
  }
  
  return items
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  overflow: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3a4b;
  color: white;
  margin-bottom: 0;
}

.logo h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.el-menu-vertical {
  border-right: none;
  height: calc(100vh - 60px);
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

.el-dropdown-link {
  cursor: pointer;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 5px;
}

.main-content {
  background-color: #f5f5f5;
  padding: 20px;
}
</style>
