<template>
  <div class="rework-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="goBack" class="back-btn">
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>返工详情</h2>
      <div class="header-actions">
        <el-button type="warning" @click="handleEdit">编辑</el-button>
        <el-button type="success" @click="handleCostDetail">成本详情</el-button>
        <el-button type="danger" @click="handle8D">8D</el-button>
      </div>
    </div>

    <!-- 基本信息 -->
    <el-card class="info-card" v-if="reworkData">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-tag :type="getReworkTypeColor(reworkData.reworkType)">{{ reworkData.reworkType }}</el-tag>
        </div>
      </template>
      
      <el-descriptions :column="3" border>
        <el-descriptions-item label="返工编号">{{ reworkData.reworkNo }}</el-descriptions-item>
        <el-descriptions-item label="投诉编号">
          <el-button v-if="reworkData.complaintNo" type="text" @click="viewComplaint(reworkData.complaintNo)">
            {{ reworkData.complaintNo }}
          </el-button>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ reworkData.customerName }}</el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ reworkData.productName }}</el-descriptions-item>
        <el-descriptions-item label="产品型号">{{ reworkData.productModel }}</el-descriptions-item>
        <el-descriptions-item label="生产批次">{{ reworkData.productionBatch }}</el-descriptions-item>
        <el-descriptions-item label="返工类型">
          <el-tag :type="getReworkTypeColor(reworkData.reworkType)">{{ reworkData.reworkType }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="返工数量">{{ reworkData.reworkQuantity }}</el-descriptions-item>
        <el-descriptions-item label="返工日期">{{ reworkData.reworkDate }}</el-descriptions-item>
        <el-descriptions-item label="完成日期">{{ reworkData.completionDate }}</el-descriptions-item>
        <el-descriptions-item label="总成本">¥{{ reworkData.totalCost.toLocaleString() }}</el-descriptions-item>
        <el-descriptions-item label="数据来源">{{ reworkData.dataSource }}</el-descriptions-item>
        <el-descriptions-item label="返工说明" :span="3">{{ reworkData.reworkDescription || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 围堵措施 -->
    <el-card class="containment-card">
      <template #header>
        <div class="card-header">
          <span>围堵措施</span>
          <el-button type="primary" size="small" @click="handleContainment">
            <el-icon><Plus /></el-icon>
            管理围堵措施
          </el-button>
        </div>
      </template>
      
      <el-table :data="containmentMeasures" border style="width: 100%">
        <el-table-column prop="location" label="地点" width="120" />
        <el-table-column prop="quantity" label="数量" width="80" />
        <el-table-column prop="responsible" label="责任人" width="100" />
        <el-table-column prop="department" label="部门" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getContainmentStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="measures" label="围堵措施" min-width="200" />
        <el-table-column prop="expectedTime" label="预计完成时间" width="120" />
        <el-table-column prop="result" label="围堵结果" min-width="150" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button type="primary" size="small" @click="editContainment(scope.row, scope.$index)">编辑</el-button>
            <el-button type="danger" size="small" @click="deleteContainment(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div v-if="containmentMeasures.length === 0" class="empty-state">
        <el-empty description="暂无围堵措施" />
      </div>
    </el-card>

    <!-- 围堵措施管理对话框 -->
    <el-dialog v-model="containmentDialogVisible" title="返工围堵措施管理" width="900px">
      <div class="containment-header">
        <div class="header-info">
          <span>返工编号：{{ reworkData?.reworkNo }}</span>
        </div>
        <el-button type="primary" size="small" @click="handleAddContainment">
          <el-icon><Plus /></el-icon>
          新增围堵
        </el-button>
      </div>

      <!-- 围堵措施列表 -->
      <div class="containment-list">
        <div v-for="(item, index) in editingContainmentList" :key="index" class="containment-item">
          <div class="item-header">
            <span class="item-number">No{{ index + 1 }}</span>
            <el-button type="text" size="small" @click="handleRemoveContainment(index)" class="remove-btn">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          
          <el-row :gutter="16" class="item-content">
            <el-col :span="6">
              <div class="form-item">
                <label>地点</label>
                <el-select v-model="item.location" placeholder="仓库" style="width: 100%">
                  <el-option label="仓库" value="仓库" />
                  <el-option label="生产线" value="生产线" />
                  <el-option label="客户现场" value="客户现场" />
                  <el-option label="运输途中" value="运输途中" />
                  <el-option label="供应商" value="供应商" />
                </el-select>
              </div>
            </el-col>
            
            <el-col :span="4">
              <div class="form-item">
                <label>数量</label>
                <el-input-number v-model="item.quantity" :min="0" style="width: 100%" />
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="form-item">
                <label>责任人</label>
                <el-select v-model="item.responsible" placeholder="张三" style="width: 100%">
                  <el-option label="张三" value="张三" />
                  <el-option label="李四" value="李四" />
                  <el-option label="王五" value="王五" />
                  <el-option label="赵六" value="赵六" />
                </el-select>
              </div>
            </el-col>
            
            <el-col :span="4">
              <div class="form-item">
                <label>部门</label>
                <el-input v-model="item.department" placeholder="PE" />
              </div>
            </el-col>
            
            <el-col :span="4">
              <div class="form-item">
                <label>状态</label>
                <el-select v-model="item.status" placeholder="进展情况" style="width: 100%">
                  <el-option label="计划中" value="计划中" />
                  <el-option label="执行中" value="执行中" />
                  <el-option label="已完成" value="已完成" />
                  <el-option label="已暂停" value="已暂停" />
                </el-select>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" class="item-content">
            <el-col :span="12">
              <div class="form-item">
                <label>围堵措施</label>
                <el-input v-model="item.measures" placeholder="请输入具体的围堵措施" />
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="form-item">
                <label>预计完成时间</label>
                <el-date-picker
                  v-model="item.expectedTime"
                  type="date"
                  placeholder="请选择日期"
                  style="width: 100%"
                />
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="form-item">
                <label>围堵结果</label>
                <el-input v-model="item.result" placeholder="围堵结果" />
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="dialog-actions">
        <el-button type="primary" @click="handleAddResponsible">添加责任人</el-button>
        <el-button type="success" @click="handleSubmitContainment">提交申请</el-button>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="containmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleContainmentSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Close } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 返工数据
const reworkData = ref(null)
const containmentDialogVisible = ref(false)

// 围堵措施数据
const containmentMeasures = ref([
  {
    location: '仓库',
    quantity: 50,
    responsible: '张三',
    department: 'PE',
    status: '已完成',
    measures: '立即停止该批次产品发货，对库存产品进行全面检查',
    expectedTime: '2024-01-18',
    result: '已完成检查，发现5台产品存在问题'
  },
  {
    location: '生产线',
    quantity: 30,
    responsible: '李四',
    department: '生产',
    status: '执行中',
    measures: '暂停生产线，对所有在制品进行检验',
    expectedTime: '2024-01-20',
    result: '正在执行中'
  }
])

const editingContainmentList = ref([])

// 返回列表
const goBack = () => {
  router.push('/rework/list')
}

// 获取返工类型颜色
const getReworkTypeColor = (type: string) => {
  switch (type) {
    case '客户返工': return 'danger'
    case '我方挑选': return 'warning'
    case '第三方挑选': return 'info'
    case '换货': return 'success'
    default: return ''
  }
}

// 获取围堵措施状态类型
const getContainmentStatusType = (status: string) => {
  switch (status) {
    case '计划中': return 'info'
    case '执行中': return 'warning'
    case '已完成': return 'success'
    case '已暂停': return 'danger'
    default: return ''
  }
}

// 处理方法
const handleEdit = () => {
  ElMessage.info('编辑功能开发中...')
}

const handleCostDetail = () => {
  ElMessage.info('跳转到成本详情页面...')
}

const handle8D = () => {
  ElMessage.info(`启动8D流程处理返工：${reworkData.value?.reworkNo}`)
}

const viewComplaint = (complaintNo: string) => {
  ElMessage.info(`查看投诉 ${complaintNo}`)
}

const handleContainment = () => {
  editingContainmentList.value = JSON.parse(JSON.stringify(containmentMeasures.value))
  containmentDialogVisible.value = true
}

const handleAddContainment = () => {
  editingContainmentList.value.push({
    location: '',
    quantity: 0,
    responsible: '',
    department: '',
    status: '计划中',
    measures: '',
    expectedTime: '',
    result: ''
  })
}

const handleRemoveContainment = (index: number) => {
  if (editingContainmentList.value.length > 1) {
    editingContainmentList.value.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一条围堵措施')
  }
}

const handleAddResponsible = () => {
  ElMessage.info('添加责任人功能开发中...')
}

const handleSubmitContainment = () => {
  ElMessage.success('围堵措施申请已提交')
}

const handleContainmentSubmit = () => {
  const hasEmptyFields = editingContainmentList.value.some(item => 
    !item.location || !item.responsible || !item.measures
  )
  
  if (hasEmptyFields) {
    ElMessage.error('请完善围堵措施信息')
    return
  }
  
  containmentMeasures.value = JSON.parse(JSON.stringify(editingContainmentList.value))
  ElMessage.success('围堵措施保存成功')
  containmentDialogVisible.value = false
}

const editContainment = (row: any, index: number) => {
  ElMessage.info(`编辑第${index + 1}条围堵措施`)
}

const deleteContainment = (index: number) => {
  ElMessageBox.confirm('确认删除此条围堵措施吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    containmentMeasures.value.splice(index, 1)
    ElMessage.success('删除成功')
  })
}

// 加载数据
const loadData = () => {
  const id = route.params.id
  // 这里应该根据ID从API获取数据，现在使用模拟数据
  reworkData.value = {
    id: 1,
    reworkNo: 'RW2024001',
    complaintNo: 'CP2024001',
    customerName: '北京汽车制造有限公司',
    productName: '发动机总成',
    productModel: 'Model A',
    productionBatch: 'B20240101',
    reworkType: '客户返工',
    reworkQuantity: 5,
    reworkDate: '2024-01-16',
    completionDate: '2024-01-18',
    totalCost: 25600,
    dataSource: 'MES/WMS',
    reworkDescription: '发动机异响问题返工维修'
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.rework-detail-page {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.page-header h2 {
  margin: 0;
  flex: 1;
  text-align: center;
}

.back-btn {
  margin-right: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.info-card, .containment-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

/* 围堵措施样式 */
.containment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.containment-list {
  max-height: 500px;
  overflow-y: auto;
}

.containment-item {
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-number {
  font-weight: 500;
  color: #409eff;
}

.remove-btn {
  color: #f56c6c;
}

.item-content {
  margin-bottom: 12px;
}

.form-item {
  margin-bottom: 8px;
}

.form-item label {
  display: block;
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.dialog-actions {
  margin: 20px 0;
  text-align: center;
}

.dialog-actions .el-button {
  margin: 0 8px;
}
</style>
