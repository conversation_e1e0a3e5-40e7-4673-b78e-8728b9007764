<template>
  <div class="analysis-form">
    <div class="page-header">
      <el-button @click="goBack" class="back-btn">
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>{{ isEdit ? '编辑分析' : '新增分析' }}</h2>
      <div class="header-actions">
        <el-button @click="handleSave">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </div>

    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 基本信息 -->
      <el-card class="form-card">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分析编号" prop="analysisNo">
              <el-input v-model="form.analysisNo" placeholder="系统自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="投诉编号" prop="complaintNo">
              <el-select v-model="form.complaintNo" placeholder="请选择投诉编号" style="width: 100%">
                <el-option label="*********" value="*********" />
                <el-option label="*********" value="*********" />
                <el-option label="*********" value="*********" />
                <el-option label="CP2024004" value="CP2024004" />
                <el-option label="CP2024005" value="CP2024005" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerName">
              <el-input v-model="form.customerName" placeholder="请输入客户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="不良分类" prop="defectCategory">
              <el-select v-model="form.defectCategory" placeholder="请选择不良分类" style="width: 100%">
                <el-option label="结构问题" value="结构问题" />
                <el-option label="电气问题" value="电气问题" />
                <el-option label="外观问题" value="外观问题" />
                <el-option label="功能性问题" value="功能性问题" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否重复发生" prop="isRecurring">
              <el-radio-group v-model="form.isRecurring">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="责任部门" prop="responsibleDept">
              <el-select v-model="form.responsibleDept" placeholder="请选择责任部门" style="width: 100%">
                <el-option label="质量部" value="质量部" />
                <el-option label="研发部" value="研发部" />
                <el-option label="制造部" value="制造部" />
                <el-option label="工艺部" value="工艺部" />
                <el-option label="电气部" value="电气部" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任人" prop="responsiblePerson">
              <el-input v-model="form.responsiblePerson" placeholder="请输入责任人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分析日期" prop="analysisDate">
              <el-date-picker
                v-model="form.analysisDate"
                type="date"
                placeholder="选择分析日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计完成日期" prop="expectedCompletionDate">
              <el-date-picker
                v-model="form.expectedCompletionDate"
                type="date"
                placeholder="选择预计完成日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 原因分析 -->
      <el-card class="form-card">
        <template #header>
          <span>原因分析</span>
        </template>

        <el-form-item label="临时对策" prop="temporaryMeasure">
          <el-input
            v-model="form.temporaryMeasure"
            type="textarea"
            :rows="3"
            placeholder="请描述临时对策"
          />
        </el-form-item>

        <el-form-item label="根本原因" prop="rootCause">
          <el-input
            v-model="form.rootCause"
            type="textarea"
            :rows="3"
            placeholder="请描述根本原因"
          />
        </el-form-item>

        <el-form-item label="5Why分析">
          <div class="why-analysis">
            <div v-for="(why, index) in form.whyAnalysis" :key="index" class="why-item">
              <div class="why-label">Why {{ index + 1 }}:</div>
              <el-input
                v-model="form.whyAnalysis[index]"
                placeholder="请输入分析内容"
                style="flex: 1; margin-right: 10px;"
              />
              <el-button
                v-if="form.whyAnalysis.length > 1"
                type="danger"
                size="small"
                @click="removeWhy(index)"
              >
                删除
              </el-button>
            </div>
            <el-button type="primary" size="small" @click="addWhy" style="margin-top: 10px;">
              添加Why
            </el-button>
          </div>
        </el-form-item>
      </el-card>

      <!-- 改善措施 -->
      <el-card class="form-card">
        <template #header>
          <span>改善措施</span>
        </template>

        <el-form-item label="改善措施" prop="improvementMeasure">
          <el-input
            v-model="form.improvementMeasure"
            type="textarea"
            :rows="4"
            placeholder="请描述改善措施"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="执行状态" prop="executionStatus">
              <el-select v-model="form.executionStatus" placeholder="请选择执行状态" style="width: 100%">
                <el-option label="未开始" value="未开始" />
                <el-option label="计划中" value="计划中" />
                <el-option label="执行中" value="执行中" />
                <el-option label="已完成" value="已完成" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分析状态" prop="analysisStatus">
              <el-select v-model="form.analysisStatus" placeholder="请选择分析状态" style="width: 100%">
                <el-option label="待分析" value="待分析" />
                <el-option label="分析中" value="分析中" />
                <el-option label="已完成" value="已完成" />
                <el-option label="已验证" value="已验证" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const isEdit = computed(() => route.params.id !== undefined)
const formRef = ref<FormInstance>()

// 表单数据
const form = ref({
  analysisNo: '',
  complaintNo: '',
  customerName: '',
  productName: '',
  defectCategory: '',
  isRecurring: false,
  responsibleDept: '',
  responsiblePerson: '',
  analysisDate: '',
  expectedCompletionDate: '',
  temporaryMeasure: '',
  rootCause: '',
  whyAnalysis: [''],
  improvementMeasure: '',
  executionStatus: '未开始',
  analysisStatus: '待分析',
  detectionCost: 0,
  repairCost: 0,
  materialCost: 0,
  transportCost: 0,
  laborCost: 0
})

// 表单验证规则
const rules: FormRules = {
  complaintNo: [{ required: true, message: '请选择投诉编号', trigger: 'change' }],
  customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
  defectCategory: [{ required: true, message: '请选择不良分类', trigger: 'change' }],
  isRecurring: [{ required: true, message: '请选择是否重复发生', trigger: 'change' }],
  responsibleDept: [{ required: true, message: '请选择责任部门', trigger: 'change' }],
  responsiblePerson: [{ required: true, message: '请输入责任人', trigger: 'blur' }],
  analysisDate: [{ required: true, message: '请选择分析日期', trigger: 'change' }]
}

// 计算总成本
const totalCost = computed(() => {
  return form.value.detectionCost + form.value.repairCost + form.value.materialCost + 
         form.value.transportCost + form.value.laborCost
})

// 返回列表
const goBack = () => {
  router.push('/analysis/list')
}

// 添加Why分析
const addWhy = () => {
  if (form.value.whyAnalysis.length < 5) {
    form.value.whyAnalysis.push('')
  } else {
    ElMessage.warning('最多只能添加5个Why分析')
  }
}

// 删除Why分析
const removeWhy = (index: number) => {
  form.value.whyAnalysis.splice(index, 1)
}

// 保存草稿
const handleSave = async () => {
  ElMessage.success('草稿保存成功')
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    ElMessage.success(isEdit.value ? '分析更新成功' : '分析创建成功')
    router.push('/analysis/list')
  } catch (error) {
    ElMessage.error('请检查表单填写是否完整')
  }
}

// 加载数据
const loadData = () => {
  if (isEdit.value) {
    const id = route.params.id
    // 这里应该根据ID从API获取数据，现在使用模拟数据
    form.value = {
      analysisNo: '*********',
      complaintNo: '*********',
      customerName: '北京汽车制造有限公司',
      productName: '发动机总成',
      defectCategory: '结构问题',
      isRecurring: true,
      responsibleDept: '质量部',
      responsiblePerson: '张工程师',
      analysisDate: '2024-01-16',
      expectedCompletionDate: '2024-02-15',
      temporaryMeasure: '立即更换异响发动机，提供临时替代品',
      rootCause: '轴承磨损导致异响，原因为润滑油品质不达标',
      whyAnalysis: [
        '发动机出现异响',
        '轴承磨损严重',
        '润滑不足',
        '润滑油品质不达标',
        '供应商质量控制不严'
      ],
      improvementMeasure: '1.更换润滑油供应商 2.加强进料检验 3.完善质量控制流程',
      executionStatus: '执行中',
      analysisStatus: '已完成',
      detectionCost: 2000,
      repairCost: 8000,
      materialCost: 10000,
      transportCost: 5000,
      laborCost: 600
    }
  } else {
    // 新增时生成分析编号
    form.value.analysisNo = 'AN' + new Date().getFullYear() + String(Date.now()).slice(-6)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.analysis-form {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.page-header h2 {
  margin: 0;
  flex: 1;
  text-align: center;
}

.back-btn {
  margin-right: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.form-card {
  margin-bottom: 20px;
}

.why-analysis {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.why-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.why-item:last-child {
  margin-bottom: 0;
}

.why-label {
  min-width: 60px;
  font-weight: 500;
  color: #606266;
  margin-right: 10px;
}
</style>
