<template>
  <div class="dashboard">
    <h1 class="page-title">客户投诉质量管理系统</h1>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pendingComplaints }}</div>
              <div class="stats-label">待处理投诉</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon processing">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.processingComplaints }}</div>
              <div class="stats-label">处理中投诉</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon completed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.completedComplaints }}</div>
              <div class="stats-label">已完成投诉</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon cost">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">¥{{ stats.totalCost.toLocaleString() }}</div>
              <div class="stats-label">本月返工成本</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快捷操作</span>
            </div>
          </template>
          <div class="actions-grid">
            <el-button type="primary" @click="$router.push('/complaints/add')">
              <el-icon><Plus /></el-icon>
              新增投诉
            </el-button>
            <el-button type="success" @click="$router.push('/rework/list')">
              <el-icon><Tools /></el-icon>
              返工管理
            </el-button>
            <el-button type="warning" @click="$router.push('/analysis/list')">
              <el-icon><DataAnalysis /></el-icon>
              原因分析
            </el-button>
            <el-button type="info" @click="$router.push('/statistics/dashboard')">
              <el-icon><TrendCharts /></el-icon>
              统计报表
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近投诉</span>
              <el-button type="text" @click="$router.push('/complaints/list')">查看全部</el-button>
            </div>
          </template>
          <div class="recent-complaints">
            <div v-for="complaint in recentComplaints" :key="complaint.id" class="complaint-item">
              <div class="complaint-info">
                <div class="complaint-title">{{ complaint.title }}</div>
                <div class="complaint-meta">{{ complaint.customer }} · {{ complaint.date }}</div>
              </div>
              <el-tag :type="getStatusType(complaint.status)">{{ complaint.status }}</el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 统计数据
const stats = ref({
  pendingComplaints: 12,
  processingComplaints: 8,
  completedComplaints: 45,
  totalCost: 125600
})

// 最近投诉
const recentComplaints = ref([
  { id: 1, title: '发动机异响问题', customer: '北京汽车', date: '2024-01-15', status: '待处理' },
  { id: 2, title: '刹车系统故障', customer: '上海汽车', date: '2024-01-14', status: '处理中' },
  { id: 3, title: '空调制冷效果差', customer: '广州汽车', date: '2024-01-13', status: '已解决' },
  { id: 4, title: '变速箱顿挫', customer: '深圳汽车', date: '2024-01-12', status: '处理中' },
  { id: 5, title: '车身漆面问题', customer: '天津汽车', date: '2024-01-11', status: '已关闭' }
])

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '待处理': return 'danger'
    case '处理中': return 'warning'
    case '已解决': return 'success'
    case '已关闭': return 'info'
    default: return ''
  }
}
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-title {
  margin-bottom: 20px;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stats-icon.pending {
  background-color: #f56c6c;
}

.stats-icon.processing {
  background-color: #e6a23c;
}

.stats-icon.completed {
  background-color: #67c23a;
}

.stats-icon.cost {
  background-color: #409eff;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.quick-actions {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.actions-grid .el-button {
  height: 60px;
  font-size: 16px;
}

.recent-complaints {
  max-height: 300px;
  overflow-y: auto;
}

.complaint-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.complaint-item:last-child {
  border-bottom: none;
}

.complaint-info {
  flex: 1;
}

.complaint-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.complaint-meta {
  font-size: 12px;
  color: #909399;
}
</style>
