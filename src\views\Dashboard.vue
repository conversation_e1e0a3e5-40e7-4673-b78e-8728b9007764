<template>
  <div class="dashboard">
    <h1 class="page-title">客户投诉质量管理系统</h1>
    
    <!-- 统计卡片 -->
    <el-row :gutter="12" class="stats-row">
      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pendingComplaints }}</div>
              <div class="stats-label">待处理投诉</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon processing">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.processingComplaints }}</div>
              <div class="stats-label">处理中投诉</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon completed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.completedComplaints }}</div>
              <div class="stats-label">已完成投诉</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon cost">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">¥{{ stats.totalCost.toLocaleString() }}</div>
              <div class="stats-label">本月返工成本</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-row :gutter="12" class="quick-actions">
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快捷操作</span>
            </div>
          </template>
          <div class="actions-grid">
            <el-button type="primary" @click="$router.push('/complaints/add')" class="action-btn">
              <el-icon><Plus /></el-icon>
              新增投诉
            </el-button>
            <el-button type="success" @click="$router.push('/rework/list')" class="action-btn">
              <el-icon><Tools /></el-icon>
              返工管理
            </el-button>
            <el-button type="warning" @click="$router.push('/analysis/list')" class="action-btn">
              <el-icon><DataAnalysis /></el-icon>
              原因分析
            </el-button>
            <el-button type="info" @click="$router.push('/statistics/dashboard')" class="action-btn">
              <el-icon><TrendCharts /></el-icon>
              统计报表
            </el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近投诉</span>
              <el-button type="text" @click="$router.push('/complaints/list')">查看全部</el-button>
            </div>
          </template>
          <div class="recent-complaints">
            <div v-for="complaint in recentComplaints" :key="complaint.id" class="complaint-item">
              <div class="complaint-info">
                <div class="complaint-title">{{ complaint.title }}</div>
                <div class="complaint-meta">{{ complaint.customer }} · {{ complaint.date }}</div>
              </div>
              <el-tag :type="getStatusType(complaint.status)">{{ complaint.status }}</el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 统计数据
const stats = ref({
  pendingComplaints: 12,
  processingComplaints: 8,
  completedComplaints: 45,
  totalCost: 125600
})

// 最近投诉
const recentComplaints = ref([
  { id: 1, title: '发动机异响问题', customer: '北京汽车', date: '2024-01-15', status: '待处理' },
  { id: 2, title: '刹车系统故障', customer: '上海汽车', date: '2024-01-14', status: '处理中' },
  { id: 3, title: '空调制冷效果差', customer: '广州汽车', date: '2024-01-13', status: '已解决' },
  { id: 4, title: '变速箱顿挫', customer: '深圳汽车', date: '2024-01-12', status: '处理中' },
  { id: 5, title: '车身漆面问题', customer: '天津汽车', date: '2024-01-11', status: '已关闭' }
])

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '待处理': return 'danger'
    case '处理中': return 'warning'
    case '已解决': return 'success'
    case '已关闭': return 'info'
    default: return ''
  }
}
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-title {
  margin-bottom: 16px;
  color: #303133;
  font-size: 20px;
  font-weight: 500;
}

.stats-row {
  margin-bottom: 16px;
}

.stats-card {
  height: 100px;
  margin-bottom: 16px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.stats-icon.pending {
  background-color: #f56c6c;
}

.stats-icon.processing {
  background-color: #e6a23c;
}

.stats-icon.completed {
  background-color: #67c23a;
}

.stats-icon.cost {
  background-color: #409eff;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.quick-actions {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.action-btn {
  height: 45px;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn .el-icon {
  margin-right: 5px;
}

.recent-complaints {
  max-height: 280px;
  overflow-y: auto;
}

.complaint-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.complaint-item:last-child {
  border-bottom: none;
}

.complaint-info {
  flex: 1;
  min-width: 0;
}

.complaint-title {
  font-size: 13px;
  color: #303133;
  margin-bottom: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.complaint-meta {
  font-size: 11px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .stats-card {
    height: 90px;
    margin-bottom: 12px;
  }

  .stats-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin-right: 10px;
  }

  .stats-number {
    font-size: 20px;
  }

  .stats-label {
    font-size: 11px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .action-btn {
    height: 45px;
    font-size: 13px;
  }

  .quick-actions {
    margin-bottom: 16px;
  }

  .quick-actions .el-col:first-child {
    margin-bottom: 16px;
  }
}
</style>
