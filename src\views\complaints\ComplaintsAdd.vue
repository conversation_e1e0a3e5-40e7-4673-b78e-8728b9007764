<template>
  <div class="complaints-add">
    <div class="page-header">
      <h2>新增客户投诉</h2>
      <div>
        <el-button @click="$router.back()">返回</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">提交</el-button>
      </div>
    </div>

    <el-card>
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3>基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="投诉编号" prop="complaintNo">
                <el-input v-model="form.complaintNo" disabled placeholder="系统自动生成" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="投诉日期" prop="complaintDate">
                <el-date-picker
                  v-model="form.complaintDate"
                  type="date"
                  placeholder="选择投诉日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 客户信息 -->
        <div class="form-section">
          <h3>客户信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户名称" prop="customerName">
                <el-input v-model="form.customerName" placeholder="请输入客户名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系方式" prop="contactInfo">
                <el-input v-model="form.contactInfo" placeholder="请输入联系方式" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="客户地址" prop="customerAddress">
                <el-input v-model="form.customerAddress" placeholder="请输入客户地址" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 产品信息 -->
        <div class="form-section">
          <h3>产品信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="产品名称" prop="productName">
                <el-select v-model="form.productName" placeholder="请选择产品名称" style="width: 100%">
                  <el-option label="发动机总成" value="发动机总成" />
                  <el-option label="刹车系统" value="刹车系统" />
                  <el-option label="空调系统" value="空调系统" />
                  <el-option label="变速箱" value="变速箱" />
                  <el-option label="车身外壳" value="车身外壳" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品型号" prop="productModel">
                <el-select v-model="form.productModel" placeholder="请选择产品型号" style="width: 100%">
                  <el-option label="Model A" value="Model A" />
                  <el-option label="Model B" value="Model B" />
                  <el-option label="Model C" value="Model C" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="生产批次" prop="productionBatch">
                <el-input v-model="form.productionBatch" placeholder="请输入生产批次" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="生产日期" prop="productionDate">
                <el-date-picker
                  v-model="form.productionDate"
                  type="date"
                  placeholder="选择生产日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 车型信息 -->
        <div class="form-section">
          <h3>车型信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户车型" prop="customerVehicleType">
                <el-input v-model="form.customerVehicleType" placeholder="请输入客户车型" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工厂车型" prop="factoryVehicleType">
                <el-input v-model="form.factoryVehicleType" placeholder="请输入工厂车型" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 不良信息 -->
        <div class="form-section">
          <h3>不良信息</h3>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="不良描述" prop="defectDescription">
                <el-input
                  v-model="form.defectDescription"
                  type="textarea"
                  :rows="4"
                  placeholder="请详细描述产品出现的问题、现象"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="不良数量" prop="defectQuantity">
                <el-input-number
                  v-model="form.defectQuantity"
                  :min="1"
                  placeholder="请输入不良数量"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="不良率(%)" prop="defectRate">
                <el-input-number
                  v-model="form.defectRate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  placeholder="请输入不良率"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="不良发生环节" prop="defectStage">
                <el-select v-model="form.defectStage" placeholder="请选择环节" style="width: 100%">
                  <el-option label="生产" value="生产" />
                  <el-option label="运输" value="运输" />
                  <el-option label="安装" value="安装" />
                  <el-option label="使用" value="使用" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="严重度" prop="severity">
                <el-select v-model="form.severity" placeholder="请选择严重度" style="width: 100%">
                  <el-option label="轻微" value="轻微" />
                  <el-option label="一般" value="一般" />
                  <el-option label="严重" value="严重" />
                  <el-option label="紧急" value="紧急" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="投诉状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                  <el-option label="待处理" value="待处理" />
                  <el-option label="处理中" value="处理中" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 附件上传 -->
        <div class="form-section">
          <h3>附件上传</h3>
          <el-form-item label="相关附件">
            <el-upload
              class="upload-demo"
              drag
              action="#"
              multiple
              :auto-upload="false"
              :file-list="fileList"
              @change="handleFileChange"
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持上传图片、视频、文档等证据文件，单个文件不超过10MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

const formRef = ref<FormInstance>()
const submitting = ref(false)
const fileList = ref([])

// 表单数据
const form = ref({
  complaintNo: '',
  complaintDate: '',
  customerName: '',
  contactInfo: '',
  customerAddress: '',
  productName: '',
  productModel: '',
  productionBatch: '',
  productionDate: '',
  customerVehicleType: '',
  factoryVehicleType: '',
  defectDescription: '',
  defectQuantity: 1,
  defectRate: 0,
  defectStage: '',
  severity: '',
  status: '待处理'
})

// 表单验证规则
const rules: FormRules = {
  complaintDate: [{ required: true, message: '请选择投诉日期', trigger: 'change' }],
  customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  contactInfo: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
  customerAddress: [{ required: true, message: '请输入客户地址', trigger: 'blur' }],
  productName: [{ required: true, message: '请选择产品名称', trigger: 'change' }],
  productModel: [{ required: true, message: '请选择产品型号', trigger: 'change' }],
  productionBatch: [{ required: true, message: '请输入生产批次', trigger: 'blur' }],
  productionDate: [{ required: true, message: '请选择生产日期', trigger: 'change' }],
  customerVehicleType: [{ required: true, message: '请输入客户车型', trigger: 'blur' }],
  factoryVehicleType: [{ required: true, message: '请输入工厂车型', trigger: 'blur' }],
  defectDescription: [{ required: true, message: '请输入不良描述', trigger: 'blur' }],
  defectQuantity: [{ required: true, message: '请输入不良数量', trigger: 'blur' }],
  defectRate: [{ required: true, message: '请输入不良率', trigger: 'blur' }],
  defectStage: [{ required: true, message: '请选择不良发生环节', trigger: 'change' }],
  severity: [{ required: true, message: '请选择严重度', trigger: 'change' }],
  status: [{ required: true, message: '请选择投诉状态', trigger: 'change' }]
}

// 生成投诉编号
const generateComplaintNo = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `CP${year}${month}${day}${random}`
}

// 文件上传处理
const handleFileChange = (file: any, fileList: any) => {
  // 这里可以处理文件上传逻辑
  console.log('文件变化:', file, fileList)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 模拟提交
    setTimeout(() => {
      ElMessage.success('投诉提交成功！')
      submitting.value = false
      // 跳转到列表页
      // router.push('/complaints/list')
    }, 1000)
  } catch (error) {
    ElMessage.error('请检查表单填写是否完整')
  }
}

onMounted(() => {
  // 生成投诉编号
  form.value.complaintNo = generateComplaintNo()
  // 设置默认投诉日期为今天
  form.value.complaintDate = new Date().toISOString().split('T')[0]
})
</script>

<style scoped>
.complaints-add {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #409eff;
  font-size: 16px;
  font-weight: 500;
}

.upload-demo {
  width: 100%;
}
</style>
