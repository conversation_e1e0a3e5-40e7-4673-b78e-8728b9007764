{"version": 3, "sources": ["../../zrender/lib/tool/convertPath.js", "../../zrender/lib/tool/dividePath.js", "../../zrender/lib/tool/morphPath.js", "../../echarts/lib/animation/morphTransitionHelper.js", "../../echarts/lib/animation/universalTransition.js", "../../echarts/index.js"], "sourcesContent": ["import { cubicSubdivide } from '../core/curve.js';\nimport PathProxy from '../core/PathProxy.js';\nvar CMD = PathProxy.CMD;\nfunction aroundEqual(a, b) {\n    return Math.abs(a - b) < 1e-5;\n}\nexport function pathToBezierCurves(path) {\n    var data = path.data;\n    var len = path.len();\n    var bezierArrayGroups = [];\n    var currentSubpath;\n    var xi = 0;\n    var yi = 0;\n    var x0 = 0;\n    var y0 = 0;\n    function createNewSubpath(x, y) {\n        if (currentSubpath && currentSubpath.length > 2) {\n            bezierArrayGroups.push(currentSubpath);\n        }\n        currentSubpath = [x, y];\n    }\n    function addLine(x0, y0, x1, y1) {\n        if (!(aroundEqual(x0, x1) && aroundEqual(y0, y1))) {\n            currentSubpath.push(x0, y0, x1, y1, x1, y1);\n        }\n    }\n    function addArc(startAngle, endAngle, cx, cy, rx, ry) {\n        var delta = Math.abs(endAngle - startAngle);\n        var len = Math.tan(delta / 4) * 4 / 3;\n        var dir = endAngle < startAngle ? -1 : 1;\n        var c1 = Math.cos(startAngle);\n        var s1 = Math.sin(startAngle);\n        var c2 = Math.cos(endAngle);\n        var s2 = Math.sin(endAngle);\n        var x1 = c1 * rx + cx;\n        var y1 = s1 * ry + cy;\n        var x4 = c2 * rx + cx;\n        var y4 = s2 * ry + cy;\n        var hx = rx * len * dir;\n        var hy = ry * len * dir;\n        currentSubpath.push(x1 - hx * s1, y1 + hy * c1, x4 + hx * s2, y4 - hy * c2, x4, y4);\n    }\n    var x1;\n    var y1;\n    var x2;\n    var y2;\n    for (var i = 0; i < len;) {\n        var cmd = data[i++];\n        var isFirst = i === 1;\n        if (isFirst) {\n            xi = data[i];\n            yi = data[i + 1];\n            x0 = xi;\n            y0 = yi;\n            if (cmd === CMD.L || cmd === CMD.C || cmd === CMD.Q) {\n                currentSubpath = [x0, y0];\n            }\n        }\n        switch (cmd) {\n            case CMD.M:\n                xi = x0 = data[i++];\n                yi = y0 = data[i++];\n                createNewSubpath(x0, y0);\n                break;\n            case CMD.L:\n                x1 = data[i++];\n                y1 = data[i++];\n                addLine(xi, yi, x1, y1);\n                xi = x1;\n                yi = y1;\n                break;\n            case CMD.C:\n                currentSubpath.push(data[i++], data[i++], data[i++], data[i++], xi = data[i++], yi = data[i++]);\n                break;\n            case CMD.Q:\n                x1 = data[i++];\n                y1 = data[i++];\n                x2 = data[i++];\n                y2 = data[i++];\n                currentSubpath.push(xi + 2 / 3 * (x1 - xi), yi + 2 / 3 * (y1 - yi), x2 + 2 / 3 * (x1 - x2), y2 + 2 / 3 * (y1 - y2), x2, y2);\n                xi = x2;\n                yi = y2;\n                break;\n            case CMD.A:\n                var cx = data[i++];\n                var cy = data[i++];\n                var rx = data[i++];\n                var ry = data[i++];\n                var startAngle = data[i++];\n                var endAngle = data[i++] + startAngle;\n                i += 1;\n                var anticlockwise = !data[i++];\n                x1 = Math.cos(startAngle) * rx + cx;\n                y1 = Math.sin(startAngle) * ry + cy;\n                if (isFirst) {\n                    x0 = x1;\n                    y0 = y1;\n                    createNewSubpath(x0, y0);\n                }\n                else {\n                    addLine(xi, yi, x1, y1);\n                }\n                xi = Math.cos(endAngle) * rx + cx;\n                yi = Math.sin(endAngle) * ry + cy;\n                var step = (anticlockwise ? -1 : 1) * Math.PI / 2;\n                for (var angle = startAngle; anticlockwise ? angle > endAngle : angle < endAngle; angle += step) {\n                    var nextAngle = anticlockwise ? Math.max(angle + step, endAngle)\n                        : Math.min(angle + step, endAngle);\n                    addArc(angle, nextAngle, cx, cy, rx, ry);\n                }\n                break;\n            case CMD.R:\n                x0 = xi = data[i++];\n                y0 = yi = data[i++];\n                x1 = x0 + data[i++];\n                y1 = y0 + data[i++];\n                createNewSubpath(x1, y0);\n                addLine(x1, y0, x1, y1);\n                addLine(x1, y1, x0, y1);\n                addLine(x0, y1, x0, y0);\n                addLine(x0, y0, x1, y0);\n                break;\n            case CMD.Z:\n                currentSubpath && addLine(xi, yi, x0, y0);\n                xi = x0;\n                yi = y0;\n                break;\n        }\n    }\n    if (currentSubpath && currentSubpath.length > 2) {\n        bezierArrayGroups.push(currentSubpath);\n    }\n    return bezierArrayGroups;\n}\nfunction adpativeBezier(x0, y0, x1, y1, x2, y2, x3, y3, out, scale) {\n    if (aroundEqual(x0, x1) && aroundEqual(y0, y1) && aroundEqual(x2, x3) && aroundEqual(y2, y3)) {\n        out.push(x3, y3);\n        return;\n    }\n    var PIXEL_DISTANCE = 2 / scale;\n    var PIXEL_DISTANCE_SQR = PIXEL_DISTANCE * PIXEL_DISTANCE;\n    var dx = x3 - x0;\n    var dy = y3 - y0;\n    var d = Math.sqrt(dx * dx + dy * dy);\n    dx /= d;\n    dy /= d;\n    var dx1 = x1 - x0;\n    var dy1 = y1 - y0;\n    var dx2 = x2 - x3;\n    var dy2 = y2 - y3;\n    var cp1LenSqr = dx1 * dx1 + dy1 * dy1;\n    var cp2LenSqr = dx2 * dx2 + dy2 * dy2;\n    if (cp1LenSqr < PIXEL_DISTANCE_SQR && cp2LenSqr < PIXEL_DISTANCE_SQR) {\n        out.push(x3, y3);\n        return;\n    }\n    var projLen1 = dx * dx1 + dy * dy1;\n    var projLen2 = -dx * dx2 - dy * dy2;\n    var d1Sqr = cp1LenSqr - projLen1 * projLen1;\n    var d2Sqr = cp2LenSqr - projLen2 * projLen2;\n    if (d1Sqr < PIXEL_DISTANCE_SQR && projLen1 >= 0\n        && d2Sqr < PIXEL_DISTANCE_SQR && projLen2 >= 0) {\n        out.push(x3, y3);\n        return;\n    }\n    var tmpSegX = [];\n    var tmpSegY = [];\n    cubicSubdivide(x0, x1, x2, x3, 0.5, tmpSegX);\n    cubicSubdivide(y0, y1, y2, y3, 0.5, tmpSegY);\n    adpativeBezier(tmpSegX[0], tmpSegY[0], tmpSegX[1], tmpSegY[1], tmpSegX[2], tmpSegY[2], tmpSegX[3], tmpSegY[3], out, scale);\n    adpativeBezier(tmpSegX[4], tmpSegY[4], tmpSegX[5], tmpSegY[5], tmpSegX[6], tmpSegY[6], tmpSegX[7], tmpSegY[7], out, scale);\n}\nexport function pathToPolygons(path, scale) {\n    var bezierArrayGroups = pathToBezierCurves(path);\n    var polygons = [];\n    scale = scale || 1;\n    for (var i = 0; i < bezierArrayGroups.length; i++) {\n        var beziers = bezierArrayGroups[i];\n        var polygon = [];\n        var x0 = beziers[0];\n        var y0 = beziers[1];\n        polygon.push(x0, y0);\n        for (var k = 2; k < beziers.length;) {\n            var x1 = beziers[k++];\n            var y1 = beziers[k++];\n            var x2 = beziers[k++];\n            var y2 = beziers[k++];\n            var x3 = beziers[k++];\n            var y3 = beziers[k++];\n            adpativeBezier(x0, y0, x1, y1, x2, y2, x3, y3, polygon, scale);\n            x0 = x3;\n            y0 = y3;\n        }\n        polygons.push(polygon);\n    }\n    return polygons;\n}\n", "import { fromPoints } from '../core/bbox.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport Point from '../core/Point.js';\nimport { map } from '../core/util.js';\nimport Polygon from '../graphic/shape/Polygon.js';\nimport Rect from '../graphic/shape/Rect.js';\nimport Sector from '../graphic/shape/Sector.js';\nimport { pathToPolygons } from './convertPath.js';\nimport { clonePath } from './path.js';\nfunction getDividingGrids(dimSize, rowDim, count) {\n    var rowSize = dimSize[rowDim];\n    var columnSize = dimSize[1 - rowDim];\n    var ratio = Math.abs(rowSize / columnSize);\n    var rowCount = Math.ceil(Math.sqrt(ratio * count));\n    var columnCount = Math.floor(count / rowCount);\n    if (columnCount === 0) {\n        columnCount = 1;\n        rowCount = count;\n    }\n    var grids = [];\n    for (var i = 0; i < rowCount; i++) {\n        grids.push(columnCount);\n    }\n    var currentCount = rowCount * columnCount;\n    var remained = count - currentCount;\n    if (remained > 0) {\n        for (var i = 0; i < remained; i++) {\n            grids[i % rowCount] += 1;\n        }\n    }\n    return grids;\n}\nfunction divideSector(sectorShape, count, outShapes) {\n    var r0 = sectorShape.r0;\n    var r = sectorShape.r;\n    var startAngle = sectorShape.startAngle;\n    var endAngle = sectorShape.endAngle;\n    var angle = Math.abs(endAngle - startAngle);\n    var arcLen = angle * r;\n    var deltaR = r - r0;\n    var isAngleRow = arcLen > Math.abs(deltaR);\n    var grids = getDividingGrids([arcLen, deltaR], isAngleRow ? 0 : 1, count);\n    var rowSize = (isAngleRow ? angle : deltaR) / grids.length;\n    for (var row = 0; row < grids.length; row++) {\n        var columnSize = (isAngleRow ? deltaR : angle) / grids[row];\n        for (var column = 0; column < grids[row]; column++) {\n            var newShape = {};\n            if (isAngleRow) {\n                newShape.startAngle = startAngle + rowSize * row;\n                newShape.endAngle = startAngle + rowSize * (row + 1);\n                newShape.r0 = r0 + columnSize * column;\n                newShape.r = r0 + columnSize * (column + 1);\n            }\n            else {\n                newShape.startAngle = startAngle + columnSize * column;\n                newShape.endAngle = startAngle + columnSize * (column + 1);\n                newShape.r0 = r0 + rowSize * row;\n                newShape.r = r0 + rowSize * (row + 1);\n            }\n            newShape.clockwise = sectorShape.clockwise;\n            newShape.cx = sectorShape.cx;\n            newShape.cy = sectorShape.cy;\n            outShapes.push(newShape);\n        }\n    }\n}\nfunction divideRect(rectShape, count, outShapes) {\n    var width = rectShape.width;\n    var height = rectShape.height;\n    var isHorizontalRow = width > height;\n    var grids = getDividingGrids([width, height], isHorizontalRow ? 0 : 1, count);\n    var rowSizeDim = isHorizontalRow ? 'width' : 'height';\n    var columnSizeDim = isHorizontalRow ? 'height' : 'width';\n    var rowDim = isHorizontalRow ? 'x' : 'y';\n    var columnDim = isHorizontalRow ? 'y' : 'x';\n    var rowSize = rectShape[rowSizeDim] / grids.length;\n    for (var row = 0; row < grids.length; row++) {\n        var columnSize = rectShape[columnSizeDim] / grids[row];\n        for (var column = 0; column < grids[row]; column++) {\n            var newShape = {};\n            newShape[rowDim] = row * rowSize;\n            newShape[columnDim] = column * columnSize;\n            newShape[rowSizeDim] = rowSize;\n            newShape[columnSizeDim] = columnSize;\n            newShape.x += rectShape.x;\n            newShape.y += rectShape.y;\n            outShapes.push(newShape);\n        }\n    }\n}\nfunction crossProduct2d(x1, y1, x2, y2) {\n    return x1 * y2 - x2 * y1;\n}\nfunction lineLineIntersect(a1x, a1y, a2x, a2y, b1x, b1y, b2x, b2y) {\n    var mx = a2x - a1x;\n    var my = a2y - a1y;\n    var nx = b2x - b1x;\n    var ny = b2y - b1y;\n    var nmCrossProduct = crossProduct2d(nx, ny, mx, my);\n    if (Math.abs(nmCrossProduct) < 1e-6) {\n        return null;\n    }\n    var b1a1x = a1x - b1x;\n    var b1a1y = a1y - b1y;\n    var p = crossProduct2d(b1a1x, b1a1y, nx, ny) / nmCrossProduct;\n    if (p < 0 || p > 1) {\n        return null;\n    }\n    return new Point(p * mx + a1x, p * my + a1y);\n}\nfunction projPtOnLine(pt, lineA, lineB) {\n    var dir = new Point();\n    Point.sub(dir, lineB, lineA);\n    dir.normalize();\n    var dir2 = new Point();\n    Point.sub(dir2, pt, lineA);\n    var len = dir2.dot(dir);\n    return len;\n}\nfunction addToPoly(poly, pt) {\n    var last = poly[poly.length - 1];\n    if (last && last[0] === pt[0] && last[1] === pt[1]) {\n        return;\n    }\n    poly.push(pt);\n}\nfunction splitPolygonByLine(points, lineA, lineB) {\n    var len = points.length;\n    var intersections = [];\n    for (var i = 0; i < len; i++) {\n        var p0 = points[i];\n        var p1 = points[(i + 1) % len];\n        var intersectionPt = lineLineIntersect(p0[0], p0[1], p1[0], p1[1], lineA.x, lineA.y, lineB.x, lineB.y);\n        if (intersectionPt) {\n            intersections.push({\n                projPt: projPtOnLine(intersectionPt, lineA, lineB),\n                pt: intersectionPt,\n                idx: i\n            });\n        }\n    }\n    if (intersections.length < 2) {\n        return [{ points: points }, { points: points }];\n    }\n    intersections.sort(function (a, b) {\n        return a.projPt - b.projPt;\n    });\n    var splitPt0 = intersections[0];\n    var splitPt1 = intersections[intersections.length - 1];\n    if (splitPt1.idx < splitPt0.idx) {\n        var tmp = splitPt0;\n        splitPt0 = splitPt1;\n        splitPt1 = tmp;\n    }\n    var splitPt0Arr = [splitPt0.pt.x, splitPt0.pt.y];\n    var splitPt1Arr = [splitPt1.pt.x, splitPt1.pt.y];\n    var newPolyA = [splitPt0Arr];\n    var newPolyB = [splitPt1Arr];\n    for (var i = splitPt0.idx + 1; i <= splitPt1.idx; i++) {\n        addToPoly(newPolyA, points[i].slice());\n    }\n    addToPoly(newPolyA, splitPt1Arr);\n    addToPoly(newPolyA, splitPt0Arr);\n    for (var i = splitPt1.idx + 1; i <= splitPt0.idx + len; i++) {\n        addToPoly(newPolyB, points[i % len].slice());\n    }\n    addToPoly(newPolyB, splitPt0Arr);\n    addToPoly(newPolyB, splitPt1Arr);\n    return [{\n            points: newPolyA\n        }, {\n            points: newPolyB\n        }];\n}\nfunction binaryDividePolygon(polygonShape) {\n    var points = polygonShape.points;\n    var min = [];\n    var max = [];\n    fromPoints(points, min, max);\n    var boundingRect = new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n    var width = boundingRect.width;\n    var height = boundingRect.height;\n    var x = boundingRect.x;\n    var y = boundingRect.y;\n    var pt0 = new Point();\n    var pt1 = new Point();\n    if (width > height) {\n        pt0.x = pt1.x = x + width / 2;\n        pt0.y = y;\n        pt1.y = y + height;\n    }\n    else {\n        pt0.y = pt1.y = y + height / 2;\n        pt0.x = x;\n        pt1.x = x + width;\n    }\n    return splitPolygonByLine(points, pt0, pt1);\n}\nfunction binaryDivideRecursive(divider, shape, count, out) {\n    if (count === 1) {\n        out.push(shape);\n    }\n    else {\n        var mid = Math.floor(count / 2);\n        var sub = divider(shape);\n        binaryDivideRecursive(divider, sub[0], mid, out);\n        binaryDivideRecursive(divider, sub[1], count - mid, out);\n    }\n    return out;\n}\nexport function clone(path, count) {\n    var paths = [];\n    for (var i = 0; i < count; i++) {\n        paths.push(clonePath(path));\n    }\n    return paths;\n}\nfunction copyPathProps(source, target) {\n    target.setStyle(source.style);\n    target.z = source.z;\n    target.z2 = source.z2;\n    target.zlevel = source.zlevel;\n}\nfunction polygonConvert(points) {\n    var out = [];\n    for (var i = 0; i < points.length;) {\n        out.push([points[i++], points[i++]]);\n    }\n    return out;\n}\nexport function split(path, count) {\n    var outShapes = [];\n    var shape = path.shape;\n    var OutShapeCtor;\n    switch (path.type) {\n        case 'rect':\n            divideRect(shape, count, outShapes);\n            OutShapeCtor = Rect;\n            break;\n        case 'sector':\n            divideSector(shape, count, outShapes);\n            OutShapeCtor = Sector;\n            break;\n        case 'circle':\n            divideSector({\n                r0: 0, r: shape.r, startAngle: 0, endAngle: Math.PI * 2,\n                cx: shape.cx, cy: shape.cy\n            }, count, outShapes);\n            OutShapeCtor = Sector;\n            break;\n        default:\n            var m = path.getComputedTransform();\n            var scale = m ? Math.sqrt(Math.max(m[0] * m[0] + m[1] * m[1], m[2] * m[2] + m[3] * m[3])) : 1;\n            var polygons = map(pathToPolygons(path.getUpdatedPathProxy(), scale), function (poly) { return polygonConvert(poly); });\n            var polygonCount = polygons.length;\n            if (polygonCount === 0) {\n                binaryDivideRecursive(binaryDividePolygon, {\n                    points: polygons[0]\n                }, count, outShapes);\n            }\n            else if (polygonCount === count) {\n                for (var i = 0; i < polygonCount; i++) {\n                    outShapes.push({\n                        points: polygons[i]\n                    });\n                }\n            }\n            else {\n                var totalArea_1 = 0;\n                var items = map(polygons, function (poly) {\n                    var min = [];\n                    var max = [];\n                    fromPoints(poly, min, max);\n                    var area = (max[1] - min[1]) * (max[0] - min[0]);\n                    totalArea_1 += area;\n                    return { poly: poly, area: area };\n                });\n                items.sort(function (a, b) { return b.area - a.area; });\n                var left = count;\n                for (var i = 0; i < polygonCount; i++) {\n                    var item = items[i];\n                    if (left <= 0) {\n                        break;\n                    }\n                    var selfCount = i === polygonCount - 1\n                        ? left\n                        : Math.ceil(item.area / totalArea_1 * count);\n                    if (selfCount < 0) {\n                        continue;\n                    }\n                    binaryDivideRecursive(binaryDividePolygon, {\n                        points: item.poly\n                    }, selfCount, outShapes);\n                    left -= selfCount;\n                }\n                ;\n            }\n            OutShapeCtor = Polygon;\n            break;\n    }\n    if (!OutShapeCtor) {\n        return clone(path, count);\n    }\n    var out = [];\n    for (var i = 0; i < outShapes.length; i++) {\n        var subPath = new OutShapeCtor();\n        subPath.setShape(outShapes[i]);\n        copyPathProps(path, subPath);\n        out.push(subPath);\n    }\n    return out;\n}\n", "import { cubicSubdivide } from '../core/curve.js';\nimport Path from '../graphic/Path.js';\nimport { defaults, map } from '../core/util.js';\nimport { lerp } from '../core/vector.js';\nimport { clonePath } from './path.js';\nimport Transformable from '../core/Transformable.js';\nimport { split } from './dividePath.js';\nimport { pathToBezierCurves } from './convertPath.js';\nfunction alignSubpath(subpath1, subpath2) {\n    var len1 = subpath1.length;\n    var len2 = subpath2.length;\n    if (len1 === len2) {\n        return [subpath1, subpath2];\n    }\n    var tmpSegX = [];\n    var tmpSegY = [];\n    var shorterPath = len1 < len2 ? subpath1 : subpath2;\n    var shorterLen = Math.min(len1, len2);\n    var diff = Math.abs(len2 - len1) / 6;\n    var shorterBezierCount = (shorterLen - 2) / 6;\n    var eachCurveSubDivCount = Math.ceil(diff / shorterBezierCount) + 1;\n    var newSubpath = [shorterPath[0], shorterPath[1]];\n    var remained = diff;\n    for (var i = 2; i < shorterLen;) {\n        var x0 = shorterPath[i - 2];\n        var y0 = shorterPath[i - 1];\n        var x1 = shorterPath[i++];\n        var y1 = shorterPath[i++];\n        var x2 = shorterPath[i++];\n        var y2 = shorterPath[i++];\n        var x3 = shorterPath[i++];\n        var y3 = shorterPath[i++];\n        if (remained <= 0) {\n            newSubpath.push(x1, y1, x2, y2, x3, y3);\n            continue;\n        }\n        var actualSubDivCount = Math.min(remained, eachCurveSubDivCount - 1) + 1;\n        for (var k = 1; k <= actualSubDivCount; k++) {\n            var p = k / actualSubDivCount;\n            cubicSubdivide(x0, x1, x2, x3, p, tmpSegX);\n            cubicSubdivide(y0, y1, y2, y3, p, tmpSegY);\n            x0 = tmpSegX[3];\n            y0 = tmpSegY[3];\n            newSubpath.push(tmpSegX[1], tmpSegY[1], tmpSegX[2], tmpSegY[2], x0, y0);\n            x1 = tmpSegX[5];\n            y1 = tmpSegY[5];\n            x2 = tmpSegX[6];\n            y2 = tmpSegY[6];\n        }\n        remained -= actualSubDivCount - 1;\n    }\n    return shorterPath === subpath1 ? [newSubpath, subpath2] : [subpath1, newSubpath];\n}\nfunction createSubpath(lastSubpathSubpath, otherSubpath) {\n    var len = lastSubpathSubpath.length;\n    var lastX = lastSubpathSubpath[len - 2];\n    var lastY = lastSubpathSubpath[len - 1];\n    var newSubpath = [];\n    for (var i = 0; i < otherSubpath.length;) {\n        newSubpath[i++] = lastX;\n        newSubpath[i++] = lastY;\n    }\n    return newSubpath;\n}\nexport function alignBezierCurves(array1, array2) {\n    var _a;\n    var lastSubpath1;\n    var lastSubpath2;\n    var newArray1 = [];\n    var newArray2 = [];\n    for (var i = 0; i < Math.max(array1.length, array2.length); i++) {\n        var subpath1 = array1[i];\n        var subpath2 = array2[i];\n        var newSubpath1 = void 0;\n        var newSubpath2 = void 0;\n        if (!subpath1) {\n            newSubpath1 = createSubpath(lastSubpath1 || subpath2, subpath2);\n            newSubpath2 = subpath2;\n        }\n        else if (!subpath2) {\n            newSubpath2 = createSubpath(lastSubpath2 || subpath1, subpath1);\n            newSubpath1 = subpath1;\n        }\n        else {\n            _a = alignSubpath(subpath1, subpath2), newSubpath1 = _a[0], newSubpath2 = _a[1];\n            lastSubpath1 = newSubpath1;\n            lastSubpath2 = newSubpath2;\n        }\n        newArray1.push(newSubpath1);\n        newArray2.push(newSubpath2);\n    }\n    return [newArray1, newArray2];\n}\nexport function centroid(array) {\n    var signedArea = 0;\n    var cx = 0;\n    var cy = 0;\n    var len = array.length;\n    for (var i = 0, j = len - 2; i < len; j = i, i += 2) {\n        var x0 = array[j];\n        var y0 = array[j + 1];\n        var x1 = array[i];\n        var y1 = array[i + 1];\n        var a = x0 * y1 - x1 * y0;\n        signedArea += a;\n        cx += (x0 + x1) * a;\n        cy += (y0 + y1) * a;\n    }\n    if (signedArea === 0) {\n        return [array[0] || 0, array[1] || 0];\n    }\n    return [cx / signedArea / 3, cy / signedArea / 3, signedArea];\n}\nfunction findBestRingOffset(fromSubBeziers, toSubBeziers, fromCp, toCp) {\n    var bezierCount = (fromSubBeziers.length - 2) / 6;\n    var bestScore = Infinity;\n    var bestOffset = 0;\n    var len = fromSubBeziers.length;\n    var len2 = len - 2;\n    for (var offset = 0; offset < bezierCount; offset++) {\n        var cursorOffset = offset * 6;\n        var score = 0;\n        for (var k = 0; k < len; k += 2) {\n            var idx = k === 0 ? cursorOffset : ((cursorOffset + k - 2) % len2 + 2);\n            var x0 = fromSubBeziers[idx] - fromCp[0];\n            var y0 = fromSubBeziers[idx + 1] - fromCp[1];\n            var x1 = toSubBeziers[k] - toCp[0];\n            var y1 = toSubBeziers[k + 1] - toCp[1];\n            var dx = x1 - x0;\n            var dy = y1 - y0;\n            score += dx * dx + dy * dy;\n        }\n        if (score < bestScore) {\n            bestScore = score;\n            bestOffset = offset;\n        }\n    }\n    return bestOffset;\n}\nfunction reverse(array) {\n    var newArr = [];\n    var len = array.length;\n    for (var i = 0; i < len; i += 2) {\n        newArr[i] = array[len - i - 2];\n        newArr[i + 1] = array[len - i - 1];\n    }\n    return newArr;\n}\nfunction findBestMorphingRotation(fromArr, toArr, searchAngleIteration, searchAngleRange) {\n    var result = [];\n    var fromNeedsReverse;\n    for (var i = 0; i < fromArr.length; i++) {\n        var fromSubpathBezier = fromArr[i];\n        var toSubpathBezier = toArr[i];\n        var fromCp = centroid(fromSubpathBezier);\n        var toCp = centroid(toSubpathBezier);\n        if (fromNeedsReverse == null) {\n            fromNeedsReverse = fromCp[2] < 0 !== toCp[2] < 0;\n        }\n        var newFromSubpathBezier = [];\n        var newToSubpathBezier = [];\n        var bestAngle = 0;\n        var bestScore = Infinity;\n        var tmpArr = [];\n        var len = fromSubpathBezier.length;\n        if (fromNeedsReverse) {\n            fromSubpathBezier = reverse(fromSubpathBezier);\n        }\n        var offset = findBestRingOffset(fromSubpathBezier, toSubpathBezier, fromCp, toCp) * 6;\n        var len2 = len - 2;\n        for (var k = 0; k < len2; k += 2) {\n            var idx = (offset + k) % len2 + 2;\n            newFromSubpathBezier[k + 2] = fromSubpathBezier[idx] - fromCp[0];\n            newFromSubpathBezier[k + 3] = fromSubpathBezier[idx + 1] - fromCp[1];\n        }\n        newFromSubpathBezier[0] = fromSubpathBezier[offset] - fromCp[0];\n        newFromSubpathBezier[1] = fromSubpathBezier[offset + 1] - fromCp[1];\n        if (searchAngleIteration > 0) {\n            var step = searchAngleRange / searchAngleIteration;\n            for (var angle = -searchAngleRange / 2; angle <= searchAngleRange / 2; angle += step) {\n                var sa = Math.sin(angle);\n                var ca = Math.cos(angle);\n                var score = 0;\n                for (var k = 0; k < fromSubpathBezier.length; k += 2) {\n                    var x0 = newFromSubpathBezier[k];\n                    var y0 = newFromSubpathBezier[k + 1];\n                    var x1 = toSubpathBezier[k] - toCp[0];\n                    var y1 = toSubpathBezier[k + 1] - toCp[1];\n                    var newX1 = x1 * ca - y1 * sa;\n                    var newY1 = x1 * sa + y1 * ca;\n                    tmpArr[k] = newX1;\n                    tmpArr[k + 1] = newY1;\n                    var dx = newX1 - x0;\n                    var dy = newY1 - y0;\n                    score += dx * dx + dy * dy;\n                }\n                if (score < bestScore) {\n                    bestScore = score;\n                    bestAngle = angle;\n                    for (var m = 0; m < tmpArr.length; m++) {\n                        newToSubpathBezier[m] = tmpArr[m];\n                    }\n                }\n            }\n        }\n        else {\n            for (var i_1 = 0; i_1 < len; i_1 += 2) {\n                newToSubpathBezier[i_1] = toSubpathBezier[i_1] - toCp[0];\n                newToSubpathBezier[i_1 + 1] = toSubpathBezier[i_1 + 1] - toCp[1];\n            }\n        }\n        result.push({\n            from: newFromSubpathBezier,\n            to: newToSubpathBezier,\n            fromCp: fromCp,\n            toCp: toCp,\n            rotation: -bestAngle\n        });\n    }\n    return result;\n}\nexport function isCombineMorphing(path) {\n    return path.__isCombineMorphing;\n}\nexport function isMorphing(el) {\n    return el.__morphT >= 0;\n}\nvar SAVED_METHOD_PREFIX = '__mOriginal_';\nfunction saveAndModifyMethod(obj, methodName, modifiers) {\n    var savedMethodName = SAVED_METHOD_PREFIX + methodName;\n    var originalMethod = obj[savedMethodName] || obj[methodName];\n    if (!obj[savedMethodName]) {\n        obj[savedMethodName] = obj[methodName];\n    }\n    var replace = modifiers.replace;\n    var after = modifiers.after;\n    var before = modifiers.before;\n    obj[methodName] = function () {\n        var args = arguments;\n        var res;\n        before && before.apply(this, args);\n        if (replace) {\n            res = replace.apply(this, args);\n        }\n        else {\n            res = originalMethod.apply(this, args);\n        }\n        after && after.apply(this, args);\n        return res;\n    };\n}\nfunction restoreMethod(obj, methodName) {\n    var savedMethodName = SAVED_METHOD_PREFIX + methodName;\n    if (obj[savedMethodName]) {\n        obj[methodName] = obj[savedMethodName];\n        obj[savedMethodName] = null;\n    }\n}\nfunction applyTransformOnBeziers(bezierCurves, mm) {\n    for (var i = 0; i < bezierCurves.length; i++) {\n        var subBeziers = bezierCurves[i];\n        for (var k = 0; k < subBeziers.length;) {\n            var x = subBeziers[k];\n            var y = subBeziers[k + 1];\n            subBeziers[k++] = mm[0] * x + mm[2] * y + mm[4];\n            subBeziers[k++] = mm[1] * x + mm[3] * y + mm[5];\n        }\n    }\n}\nfunction prepareMorphPath(fromPath, toPath) {\n    var fromPathProxy = fromPath.getUpdatedPathProxy();\n    var toPathProxy = toPath.getUpdatedPathProxy();\n    var _a = alignBezierCurves(pathToBezierCurves(fromPathProxy), pathToBezierCurves(toPathProxy)), fromBezierCurves = _a[0], toBezierCurves = _a[1];\n    var fromPathTransform = fromPath.getComputedTransform();\n    var toPathTransform = toPath.getComputedTransform();\n    function updateIdentityTransform() {\n        this.transform = null;\n    }\n    fromPathTransform && applyTransformOnBeziers(fromBezierCurves, fromPathTransform);\n    toPathTransform && applyTransformOnBeziers(toBezierCurves, toPathTransform);\n    saveAndModifyMethod(toPath, 'updateTransform', { replace: updateIdentityTransform });\n    toPath.transform = null;\n    var morphingData = findBestMorphingRotation(fromBezierCurves, toBezierCurves, 10, Math.PI);\n    var tmpArr = [];\n    saveAndModifyMethod(toPath, 'buildPath', { replace: function (path) {\n            var t = toPath.__morphT;\n            var onet = 1 - t;\n            var newCp = [];\n            for (var i = 0; i < morphingData.length; i++) {\n                var item = morphingData[i];\n                var from = item.from;\n                var to = item.to;\n                var angle = item.rotation * t;\n                var fromCp = item.fromCp;\n                var toCp = item.toCp;\n                var sa = Math.sin(angle);\n                var ca = Math.cos(angle);\n                lerp(newCp, fromCp, toCp, t);\n                for (var m = 0; m < from.length; m += 2) {\n                    var x0_1 = from[m];\n                    var y0_1 = from[m + 1];\n                    var x1 = to[m];\n                    var y1 = to[m + 1];\n                    var x = x0_1 * onet + x1 * t;\n                    var y = y0_1 * onet + y1 * t;\n                    tmpArr[m] = (x * ca - y * sa) + newCp[0];\n                    tmpArr[m + 1] = (x * sa + y * ca) + newCp[1];\n                }\n                var x0 = tmpArr[0];\n                var y0 = tmpArr[1];\n                path.moveTo(x0, y0);\n                for (var m = 2; m < from.length;) {\n                    var x1 = tmpArr[m++];\n                    var y1 = tmpArr[m++];\n                    var x2 = tmpArr[m++];\n                    var y2 = tmpArr[m++];\n                    var x3 = tmpArr[m++];\n                    var y3 = tmpArr[m++];\n                    if (x0 === x1 && y0 === y1 && x2 === x3 && y2 === y3) {\n                        path.lineTo(x3, y3);\n                    }\n                    else {\n                        path.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n                    }\n                    x0 = x3;\n                    y0 = y3;\n                }\n            }\n        } });\n}\nexport function morphPath(fromPath, toPath, animationOpts) {\n    if (!fromPath || !toPath) {\n        return toPath;\n    }\n    var oldDone = animationOpts.done;\n    var oldDuring = animationOpts.during;\n    prepareMorphPath(fromPath, toPath);\n    toPath.__morphT = 0;\n    function restoreToPath() {\n        restoreMethod(toPath, 'buildPath');\n        restoreMethod(toPath, 'updateTransform');\n        toPath.__morphT = -1;\n        toPath.createPathProxy();\n        toPath.dirtyShape();\n    }\n    toPath.animateTo({\n        __morphT: 1\n    }, defaults({\n        during: function (p) {\n            toPath.dirtyShape();\n            oldDuring && oldDuring(p);\n        },\n        done: function () {\n            restoreToPath();\n            oldDone && oldDone();\n        }\n    }, animationOpts));\n    return toPath;\n}\nfunction hilbert(x, y, minX, minY, maxX, maxY) {\n    var bits = 16;\n    x = (maxX === minX) ? 0 : Math.round(32767 * (x - minX) / (maxX - minX));\n    y = (maxY === minY) ? 0 : Math.round(32767 * (y - minY) / (maxY - minY));\n    var d = 0;\n    var tmp;\n    for (var s = (1 << bits) / 2; s > 0; s /= 2) {\n        var rx = 0;\n        var ry = 0;\n        if ((x & s) > 0) {\n            rx = 1;\n        }\n        if ((y & s) > 0) {\n            ry = 1;\n        }\n        d += s * s * ((3 * rx) ^ ry);\n        if (ry === 0) {\n            if (rx === 1) {\n                x = s - 1 - x;\n                y = s - 1 - y;\n            }\n            tmp = x;\n            x = y;\n            y = tmp;\n        }\n    }\n    return d;\n}\nfunction sortPaths(pathList) {\n    var xMin = Infinity;\n    var yMin = Infinity;\n    var xMax = -Infinity;\n    var yMax = -Infinity;\n    var cps = map(pathList, function (path) {\n        var rect = path.getBoundingRect();\n        var m = path.getComputedTransform();\n        var x = rect.x + rect.width / 2 + (m ? m[4] : 0);\n        var y = rect.y + rect.height / 2 + (m ? m[5] : 0);\n        xMin = Math.min(x, xMin);\n        yMin = Math.min(y, yMin);\n        xMax = Math.max(x, xMax);\n        yMax = Math.max(y, yMax);\n        return [x, y];\n    });\n    var items = map(cps, function (cp, idx) {\n        return {\n            cp: cp,\n            z: hilbert(cp[0], cp[1], xMin, yMin, xMax, yMax),\n            path: pathList[idx]\n        };\n    });\n    return items.sort(function (a, b) { return a.z - b.z; }).map(function (item) { return item.path; });\n}\n;\nfunction defaultDividePath(param) {\n    return split(param.path, param.count);\n}\nfunction createEmptyReturn() {\n    return {\n        fromIndividuals: [],\n        toIndividuals: [],\n        count: 0\n    };\n}\nexport function combineMorph(fromList, toPath, animationOpts) {\n    var fromPathList = [];\n    function addFromPath(fromList) {\n        for (var i = 0; i < fromList.length; i++) {\n            var from = fromList[i];\n            if (isCombineMorphing(from)) {\n                addFromPath(from.childrenRef());\n            }\n            else if (from instanceof Path) {\n                fromPathList.push(from);\n            }\n        }\n    }\n    addFromPath(fromList);\n    var separateCount = fromPathList.length;\n    if (!separateCount) {\n        return createEmptyReturn();\n    }\n    var dividePath = animationOpts.dividePath || defaultDividePath;\n    var toSubPathList = dividePath({\n        path: toPath, count: separateCount\n    });\n    if (toSubPathList.length !== separateCount) {\n        console.error('Invalid morphing: unmatched splitted path');\n        return createEmptyReturn();\n    }\n    fromPathList = sortPaths(fromPathList);\n    toSubPathList = sortPaths(toSubPathList);\n    var oldDone = animationOpts.done;\n    var oldDuring = animationOpts.during;\n    var individualDelay = animationOpts.individualDelay;\n    var identityTransform = new Transformable();\n    for (var i = 0; i < separateCount; i++) {\n        var from = fromPathList[i];\n        var to = toSubPathList[i];\n        to.parent = toPath;\n        to.copyTransform(identityTransform);\n        if (!individualDelay) {\n            prepareMorphPath(from, to);\n        }\n    }\n    toPath.__isCombineMorphing = true;\n    toPath.childrenRef = function () {\n        return toSubPathList;\n    };\n    function addToSubPathListToZr(zr) {\n        for (var i = 0; i < toSubPathList.length; i++) {\n            toSubPathList[i].addSelfToZr(zr);\n        }\n    }\n    saveAndModifyMethod(toPath, 'addSelfToZr', {\n        after: function (zr) {\n            addToSubPathListToZr(zr);\n        }\n    });\n    saveAndModifyMethod(toPath, 'removeSelfFromZr', {\n        after: function (zr) {\n            for (var i = 0; i < toSubPathList.length; i++) {\n                toSubPathList[i].removeSelfFromZr(zr);\n            }\n        }\n    });\n    function restoreToPath() {\n        toPath.__isCombineMorphing = false;\n        toPath.__morphT = -1;\n        toPath.childrenRef = null;\n        restoreMethod(toPath, 'addSelfToZr');\n        restoreMethod(toPath, 'removeSelfFromZr');\n    }\n    var toLen = toSubPathList.length;\n    if (individualDelay) {\n        var animating_1 = toLen;\n        var eachDone = function () {\n            animating_1--;\n            if (animating_1 === 0) {\n                restoreToPath();\n                oldDone && oldDone();\n            }\n        };\n        for (var i = 0; i < toLen; i++) {\n            var indivdualAnimationOpts = individualDelay ? defaults({\n                delay: (animationOpts.delay || 0) + individualDelay(i, toLen, fromPathList[i], toSubPathList[i]),\n                done: eachDone\n            }, animationOpts) : animationOpts;\n            morphPath(fromPathList[i], toSubPathList[i], indivdualAnimationOpts);\n        }\n    }\n    else {\n        toPath.__morphT = 0;\n        toPath.animateTo({\n            __morphT: 1\n        }, defaults({\n            during: function (p) {\n                for (var i = 0; i < toLen; i++) {\n                    var child = toSubPathList[i];\n                    child.__morphT = toPath.__morphT;\n                    child.dirtyShape();\n                }\n                oldDuring && oldDuring(p);\n            },\n            done: function () {\n                restoreToPath();\n                for (var i = 0; i < fromList.length; i++) {\n                    restoreMethod(fromList[i], 'updateTransform');\n                }\n                oldDone && oldDone();\n            }\n        }, animationOpts));\n    }\n    if (toPath.__zr) {\n        addToSubPathListToZr(toPath.__zr);\n    }\n    return {\n        fromIndividuals: fromPathList,\n        toIndividuals: toSubPathList,\n        count: toLen\n    };\n}\nexport function separateMorph(fromPath, toPathList, animationOpts) {\n    var toLen = toPathList.length;\n    var fromPathList = [];\n    var dividePath = animationOpts.dividePath || defaultDividePath;\n    function addFromPath(fromList) {\n        for (var i = 0; i < fromList.length; i++) {\n            var from = fromList[i];\n            if (isCombineMorphing(from)) {\n                addFromPath(from.childrenRef());\n            }\n            else if (from instanceof Path) {\n                fromPathList.push(from);\n            }\n        }\n    }\n    if (isCombineMorphing(fromPath)) {\n        addFromPath(fromPath.childrenRef());\n        var fromLen = fromPathList.length;\n        if (fromLen < toLen) {\n            var k = 0;\n            for (var i = fromLen; i < toLen; i++) {\n                fromPathList.push(clonePath(fromPathList[k++ % fromLen]));\n            }\n        }\n        fromPathList.length = toLen;\n    }\n    else {\n        fromPathList = dividePath({ path: fromPath, count: toLen });\n        var fromPathTransform = fromPath.getComputedTransform();\n        for (var i = 0; i < fromPathList.length; i++) {\n            fromPathList[i].setLocalTransform(fromPathTransform);\n        }\n        if (fromPathList.length !== toLen) {\n            console.error('Invalid morphing: unmatched splitted path');\n            return createEmptyReturn();\n        }\n    }\n    fromPathList = sortPaths(fromPathList);\n    toPathList = sortPaths(toPathList);\n    var individualDelay = animationOpts.individualDelay;\n    for (var i = 0; i < toLen; i++) {\n        var indivdualAnimationOpts = individualDelay ? defaults({\n            delay: (animationOpts.delay || 0) + individualDelay(i, toLen, fromPathList[i], toPathList[i])\n        }, animationOpts) : animationOpts;\n        morphPath(fromPathList[i], toPathList[i], indivdualAnimationOpts);\n    }\n    return {\n        fromIndividuals: fromPathList,\n        toIndividuals: toPathList,\n        count: toPathList.length\n    };\n}\nexport { split as defaultDividePath };\n", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { separateMorph, combineMorph, morphPath, isCombineMorphing } from 'zrender/lib/tool/morphPath.js';\nimport { Path } from '../util/graphic.js';\nimport { defaults, isArray } from 'zrender/lib/core/util.js';\nimport { getAnimationConfig } from './basicTransition.js';\nimport { clonePath } from 'zrender/lib/tool/path.js';\nfunction isMultiple(elements) {\n  return isArray(elements[0]);\n}\nfunction prepareMorphBatches(one, many) {\n  var batches = [];\n  var batchCount = one.length;\n  for (var i = 0; i < batchCount; i++) {\n    batches.push({\n      one: one[i],\n      many: []\n    });\n  }\n  for (var i = 0; i < many.length; i++) {\n    var len = many[i].length;\n    var k = void 0;\n    for (k = 0; k < len; k++) {\n      batches[k % batchCount].many.push(many[i][k]);\n    }\n  }\n  var off = 0;\n  // If one has more paths than each one of many. average them.\n  for (var i = batchCount - 1; i >= 0; i--) {\n    if (!batches[i].many.length) {\n      var moveFrom = batches[off].many;\n      if (moveFrom.length <= 1) {\n        // Not enough\n        // Start from the first one.\n        if (off) {\n          off = 0;\n        } else {\n          return batches;\n        }\n      }\n      var len = moveFrom.length;\n      var mid = Math.ceil(len / 2);\n      batches[i].many = moveFrom.slice(mid, len);\n      batches[off].many = moveFrom.slice(0, mid);\n      off++;\n    }\n  }\n  return batches;\n}\nvar pathDividers = {\n  clone: function (params) {\n    var ret = [];\n    // Fitting the alpha\n    var approxOpacity = 1 - Math.pow(1 - params.path.style.opacity, 1 / params.count);\n    for (var i = 0; i < params.count; i++) {\n      var cloned = clonePath(params.path);\n      cloned.setStyle('opacity', approxOpacity);\n      ret.push(cloned);\n    }\n    return ret;\n  },\n  // Use the default divider\n  split: null\n};\nexport function applyMorphAnimation(from, to, divideShape, seriesModel, dataIndex, animateOtherProps) {\n  if (!from.length || !to.length) {\n    return;\n  }\n  var updateAnimationCfg = getAnimationConfig('update', seriesModel, dataIndex);\n  if (!(updateAnimationCfg && updateAnimationCfg.duration > 0)) {\n    return;\n  }\n  var animationDelay = seriesModel.getModel('universalTransition').get('delay');\n  var animationCfg = Object.assign({\n    // Need to setToFinal so the further calculation based on the style can be correct.\n    // Like emphasis color.\n    setToFinal: true\n  }, updateAnimationCfg);\n  var many;\n  var one;\n  if (isMultiple(from)) {\n    // manyToOne\n    many = from;\n    one = to;\n  }\n  if (isMultiple(to)) {\n    // oneToMany\n    many = to;\n    one = from;\n  }\n  function morphOneBatch(batch, fromIsMany, animateIndex, animateCount, forceManyOne) {\n    var batchMany = batch.many;\n    var batchOne = batch.one;\n    if (batchMany.length === 1 && !forceManyOne) {\n      // Is one to one\n      var batchFrom = fromIsMany ? batchMany[0] : batchOne;\n      var batchTo = fromIsMany ? batchOne : batchMany[0];\n      if (isCombineMorphing(batchFrom)) {\n        // Keep doing combine animation.\n        morphOneBatch({\n          many: [batchFrom],\n          one: batchTo\n        }, true, animateIndex, animateCount, true);\n      } else {\n        var individualAnimationCfg = animationDelay ? defaults({\n          delay: animationDelay(animateIndex, animateCount)\n        }, animationCfg) : animationCfg;\n        morphPath(batchFrom, batchTo, individualAnimationCfg);\n        animateOtherProps(batchFrom, batchTo, batchFrom, batchTo, individualAnimationCfg);\n      }\n    } else {\n      var separateAnimationCfg = defaults({\n        dividePath: pathDividers[divideShape],\n        individualDelay: animationDelay && function (idx, count, fromPath, toPath) {\n          return animationDelay(idx + animateIndex, animateCount);\n        }\n      }, animationCfg);\n      var _a = fromIsMany ? combineMorph(batchMany, batchOne, separateAnimationCfg) : separateMorph(batchOne, batchMany, separateAnimationCfg),\n        fromIndividuals = _a.fromIndividuals,\n        toIndividuals = _a.toIndividuals;\n      var count = fromIndividuals.length;\n      for (var k = 0; k < count; k++) {\n        var individualAnimationCfg = animationDelay ? defaults({\n          delay: animationDelay(k, count)\n        }, animationCfg) : animationCfg;\n        animateOtherProps(fromIndividuals[k], toIndividuals[k], fromIsMany ? batchMany[k] : batch.one, fromIsMany ? batch.one : batchMany[k], individualAnimationCfg);\n      }\n    }\n  }\n  var fromIsMany = many ? many === from\n  // Is one to one. If the path number not match. also needs do merge and separate morphing.\n  : from.length > to.length;\n  var morphBatches = many ? prepareMorphBatches(one, many) : prepareMorphBatches(fromIsMany ? to : from, [fromIsMany ? from : to]);\n  var animateCount = 0;\n  for (var i = 0; i < morphBatches.length; i++) {\n    animateCount += morphBatches[i].many.length;\n  }\n  var animateIndex = 0;\n  for (var i = 0; i < morphBatches.length; i++) {\n    morphOneBatch(morphBatches[i], fromIsMany, animateIndex, animateCount);\n    animateIndex += morphBatches[i].many.length;\n  }\n}\nexport function getPathList(elements) {\n  if (!elements) {\n    return [];\n  }\n  if (isArray(elements)) {\n    var pathList_1 = [];\n    for (var i = 0; i < elements.length; i++) {\n      pathList_1.push(getPathList(elements[i]));\n    }\n    return pathList_1;\n  }\n  var pathList = [];\n  elements.traverse(function (el) {\n    if (el instanceof Path && !el.disableMorphing && !el.invisible && !el.ignore) {\n      pathList.push(el);\n    }\n  });\n  return pathList;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Universal transitions that can animate between any shapes(series) and any properties in any amounts.\nimport { SERIES_UNIVERSAL_TRANSITION_PROP } from '../model/Series.js';\nimport { createHashMap, each, map, filter, isArray, extend } from 'zrender/lib/core/util.js';\nimport { applyMorphAnimation, getPathList } from './morphTransitionHelper.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { initProps } from '../util/graphic.js';\nimport DataDiffer from '../data/DataDiffer.js';\nimport { makeInner, normalizeToArray } from '../util/model.js';\nimport { warn } from '../util/log.js';\nimport { getAnimationConfig, getOldStyle } from './basicTransition.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nvar DATA_COUNT_THRESHOLD = 1e4;\nvar TRANSITION_NONE = 0;\nvar TRANSITION_P2C = 1;\nvar TRANSITION_C2P = 2;\n;\nvar getUniversalTransitionGlobalStore = makeInner();\nfunction getDimension(data, visualDimension) {\n  var dimensions = data.dimensions;\n  for (var i = 0; i < dimensions.length; i++) {\n    var dimInfo = data.getDimensionInfo(dimensions[i]);\n    if (dimInfo && dimInfo.otherDims[visualDimension] === 0) {\n      return dimensions[i];\n    }\n  }\n}\n// get value by dimension. (only get value of itemGroupId or childGroupId, so convert it to string)\nfunction getValueByDimension(data, dataIndex, dimension) {\n  var dimInfo = data.getDimensionInfo(dimension);\n  var dimOrdinalMeta = dimInfo && dimInfo.ordinalMeta;\n  if (dimInfo) {\n    var value = data.get(dimInfo.name, dataIndex);\n    if (dimOrdinalMeta) {\n      return dimOrdinalMeta.categories[value] || value + '';\n    }\n    return value + '';\n  }\n}\nfunction getGroupId(data, dataIndex, dataGroupId, isChild) {\n  // try to get groupId from encode\n  var visualDimension = isChild ? 'itemChildGroupId' : 'itemGroupId';\n  var groupIdDim = getDimension(data, visualDimension);\n  if (groupIdDim) {\n    var groupId = getValueByDimension(data, dataIndex, groupIdDim);\n    return groupId;\n  }\n  // try to get groupId from raw data item\n  var rawDataItem = data.getRawDataItem(dataIndex);\n  var property = isChild ? 'childGroupId' : 'groupId';\n  if (rawDataItem && rawDataItem[property]) {\n    return rawDataItem[property] + '';\n  }\n  // fallback\n  if (isChild) {\n    return;\n  }\n  // try to use series.dataGroupId as groupId, otherwise use dataItem's id as groupId\n  return dataGroupId || data.getId(dataIndex);\n}\n// flatten all data items from different serieses into one arrary\nfunction flattenDataDiffItems(list) {\n  var items = [];\n  each(list, function (seriesInfo) {\n    var data = seriesInfo.data;\n    var dataGroupId = seriesInfo.dataGroupId;\n    if (data.count() > DATA_COUNT_THRESHOLD) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('Universal transition is disabled on large data > 10k.');\n      }\n      return;\n    }\n    var indices = data.getIndices();\n    for (var dataIndex = 0; dataIndex < indices.length; dataIndex++) {\n      items.push({\n        data: data,\n        groupId: getGroupId(data, dataIndex, dataGroupId, false),\n        childGroupId: getGroupId(data, dataIndex, dataGroupId, true),\n        divide: seriesInfo.divide,\n        dataIndex: dataIndex\n      });\n    }\n  });\n  return items;\n}\nfunction fadeInElement(newEl, newSeries, newIndex) {\n  newEl.traverse(function (el) {\n    if (el instanceof Path) {\n      // TODO use fade in animation for target element.\n      initProps(el, {\n        style: {\n          opacity: 0\n        }\n      }, newSeries, {\n        dataIndex: newIndex,\n        isFrom: true\n      });\n    }\n  });\n}\nfunction removeEl(el) {\n  if (el.parent) {\n    // Bake parent transform to element.\n    // So it can still have proper transform to transition after it's removed.\n    var computedTransform = el.getComputedTransform();\n    el.setLocalTransform(computedTransform);\n    el.parent.remove(el);\n  }\n}\nfunction stopAnimation(el) {\n  el.stopAnimation();\n  if (el.isGroup) {\n    el.traverse(function (child) {\n      child.stopAnimation();\n    });\n  }\n}\nfunction animateElementStyles(el, dataIndex, seriesModel) {\n  var animationConfig = getAnimationConfig('update', seriesModel, dataIndex);\n  animationConfig && el.traverse(function (child) {\n    if (child instanceof Displayable) {\n      var oldStyle = getOldStyle(child);\n      if (oldStyle) {\n        child.animateFrom({\n          style: oldStyle\n        }, animationConfig);\n      }\n    }\n  });\n}\nfunction isAllIdSame(oldDiffItems, newDiffItems) {\n  var len = oldDiffItems.length;\n  if (len !== newDiffItems.length) {\n    return false;\n  }\n  for (var i = 0; i < len; i++) {\n    var oldItem = oldDiffItems[i];\n    var newItem = newDiffItems[i];\n    if (oldItem.data.getId(oldItem.dataIndex) !== newItem.data.getId(newItem.dataIndex)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction transitionBetween(oldList, newList, api) {\n  var oldDiffItems = flattenDataDiffItems(oldList);\n  var newDiffItems = flattenDataDiffItems(newList);\n  function updateMorphingPathProps(from, to, rawFrom, rawTo, animationCfg) {\n    if (rawFrom || from) {\n      to.animateFrom({\n        style: rawFrom && rawFrom !== from\n        // dividingMethod like clone may override the style(opacity)\n        // So extend it to raw style.\n        ? extend(extend({}, rawFrom.style), from.style) : from.style\n      }, animationCfg);\n    }\n  }\n  var hasMorphAnimation = false;\n  /**\r\n   * With groupId and childGroupId, we can build parent-child relationships between dataItems.\r\n   * However, we should mind the parent-child \"direction\" between old and new options.\r\n   *\r\n   * For example, suppose we have two dataItems from two series.data:\r\n   *\r\n   * dataA: [                          dataB: [\r\n   *   {                                 {\r\n   *     value: 5,                         value: 3,\r\n   *     groupId: 'creatures',             groupId: 'animals',\r\n   *     childGroupId: 'animals'           childGroupId: 'dogs'\r\n   *   },                                },\r\n   *   ...                               ...\r\n   * ]                                 ]\r\n   *\r\n   * where dataA is belong to optionA and dataB is belong to optionB.\r\n   *\r\n   * When we `setOption(optionB)` from optionA, we choose childGroupId of dataItemA and groupId of\r\n   * dataItemB as keys so the two keys are matched (both are 'animals'), then universalTransition\r\n   * will work. This derection is \"parent -> child\".\r\n   *\r\n   * If we `setOption(optionA)` from optionB, we also choose groupId of dataItemB and childGroupId\r\n   * of dataItemA as keys and universalTransition will work. This derection is \"child -> parent\".\r\n   *\r\n   * If there is no childGroupId specified, which means no multiLevelDrillDown/Up is needed and no\r\n   * parent-child relationship exists. This direction is \"none\".\r\n   *\r\n   * So we need to know whether to use groupId or childGroupId as the key when we call the keyGetter\r\n   * functions. Thus, we need to decide the direction first.\r\n   *\r\n   * The rule is:\r\n   *\r\n   * if (all childGroupIds in oldDiffItems and all groupIds in newDiffItems have common value) {\r\n   *   direction = 'parent -> child';\r\n   * } else if (all groupIds in oldDiffItems and all childGroupIds in newDiffItems have common value) {\r\n   *   direction = 'child -> parent';\r\n   * } else {\r\n   *   direction = 'none';\r\n   * }\r\n   */\n  var direction = TRANSITION_NONE;\n  // find all groupIds and childGroupIds from oldDiffItems\n  var oldGroupIds = createHashMap();\n  var oldChildGroupIds = createHashMap();\n  oldDiffItems.forEach(function (item) {\n    item.groupId && oldGroupIds.set(item.groupId, true);\n    item.childGroupId && oldChildGroupIds.set(item.childGroupId, true);\n  });\n  // traverse newDiffItems and decide the direction according to the rule\n  for (var i = 0; i < newDiffItems.length; i++) {\n    var newGroupId = newDiffItems[i].groupId;\n    if (oldChildGroupIds.get(newGroupId)) {\n      direction = TRANSITION_P2C;\n      break;\n    }\n    var newChildGroupId = newDiffItems[i].childGroupId;\n    if (newChildGroupId && oldGroupIds.get(newChildGroupId)) {\n      direction = TRANSITION_C2P;\n      break;\n    }\n  }\n  function createKeyGetter(isOld, onlyGetId) {\n    return function (diffItem) {\n      var data = diffItem.data;\n      var dataIndex = diffItem.dataIndex;\n      // TODO if specified dim\n      if (onlyGetId) {\n        return data.getId(dataIndex);\n      }\n      if (isOld) {\n        return direction === TRANSITION_P2C ? diffItem.childGroupId : diffItem.groupId;\n      } else {\n        return direction === TRANSITION_C2P ? diffItem.childGroupId : diffItem.groupId;\n      }\n    };\n  }\n  // Use id if it's very likely to be an one to one animation\n  // It's more robust than groupId\n  // TODO Check if key dimension is specified.\n  var useId = isAllIdSame(oldDiffItems, newDiffItems);\n  var isElementStillInChart = {};\n  if (!useId) {\n    // We may have different diff strategy with basicTransition if we use other dimension as key.\n    // If so, we can't simply check if oldEl is same with newEl. We need a map to check if oldEl is still being used in the new chart.\n    // We can't use the elements that already being morphed. Let it keep it's original basic transition.\n    for (var i = 0; i < newDiffItems.length; i++) {\n      var newItem = newDiffItems[i];\n      var el = newItem.data.getItemGraphicEl(newItem.dataIndex);\n      if (el) {\n        isElementStillInChart[el.id] = true;\n      }\n    }\n  }\n  function updateOneToOne(newIndex, oldIndex) {\n    var oldItem = oldDiffItems[oldIndex];\n    var newItem = newDiffItems[newIndex];\n    var newSeries = newItem.data.hostModel;\n    // TODO Mark this elements is morphed and don't morph them anymore\n    var oldEl = oldItem.data.getItemGraphicEl(oldItem.dataIndex);\n    var newEl = newItem.data.getItemGraphicEl(newItem.dataIndex);\n    // Can't handle same elements.\n    if (oldEl === newEl) {\n      newEl && animateElementStyles(newEl, newItem.dataIndex, newSeries);\n      return;\n    }\n    if (\n    // We can't use the elements that already being morphed\n    oldEl && isElementStillInChart[oldEl.id]) {\n      return;\n    }\n    if (newEl) {\n      // TODO: If keep animating the group in case\n      // some of the elements don't want to be morphed.\n      // TODO Label?\n      stopAnimation(newEl);\n      if (oldEl) {\n        stopAnimation(oldEl);\n        // If old element is doing leaving animation. stop it and remove it immediately.\n        removeEl(oldEl);\n        hasMorphAnimation = true;\n        applyMorphAnimation(getPathList(oldEl), getPathList(newEl), newItem.divide, newSeries, newIndex, updateMorphingPathProps);\n      } else {\n        fadeInElement(newEl, newSeries, newIndex);\n      }\n    }\n    // else keep oldEl leaving animation.\n  }\n  new DataDiffer(oldDiffItems, newDiffItems, createKeyGetter(true, useId), createKeyGetter(false, useId), null, 'multiple').update(updateOneToOne).updateManyToOne(function (newIndex, oldIndices) {\n    var newItem = newDiffItems[newIndex];\n    var newData = newItem.data;\n    var newSeries = newData.hostModel;\n    var newEl = newData.getItemGraphicEl(newItem.dataIndex);\n    var oldElsList = filter(map(oldIndices, function (idx) {\n      return oldDiffItems[idx].data.getItemGraphicEl(oldDiffItems[idx].dataIndex);\n    }), function (oldEl) {\n      return oldEl && oldEl !== newEl && !isElementStillInChart[oldEl.id];\n    });\n    if (newEl) {\n      stopAnimation(newEl);\n      if (oldElsList.length) {\n        // If old element is doing leaving animation. stop it and remove it immediately.\n        each(oldElsList, function (oldEl) {\n          stopAnimation(oldEl);\n          removeEl(oldEl);\n        });\n        hasMorphAnimation = true;\n        applyMorphAnimation(getPathList(oldElsList), getPathList(newEl), newItem.divide, newSeries, newIndex, updateMorphingPathProps);\n      } else {\n        fadeInElement(newEl, newSeries, newItem.dataIndex);\n      }\n    }\n    // else keep oldEl leaving animation.\n  }).updateOneToMany(function (newIndices, oldIndex) {\n    var oldItem = oldDiffItems[oldIndex];\n    var oldEl = oldItem.data.getItemGraphicEl(oldItem.dataIndex);\n    // We can't use the elements that already being morphed\n    if (oldEl && isElementStillInChart[oldEl.id]) {\n      return;\n    }\n    var newElsList = filter(map(newIndices, function (idx) {\n      return newDiffItems[idx].data.getItemGraphicEl(newDiffItems[idx].dataIndex);\n    }), function (el) {\n      return el && el !== oldEl;\n    });\n    var newSeris = newDiffItems[newIndices[0]].data.hostModel;\n    if (newElsList.length) {\n      each(newElsList, function (newEl) {\n        return stopAnimation(newEl);\n      });\n      if (oldEl) {\n        stopAnimation(oldEl);\n        // If old element is doing leaving animation. stop it and remove it immediately.\n        removeEl(oldEl);\n        hasMorphAnimation = true;\n        applyMorphAnimation(getPathList(oldEl), getPathList(newElsList), oldItem.divide,\n        // Use divide on old.\n        newSeris, newIndices[0], updateMorphingPathProps);\n      } else {\n        each(newElsList, function (newEl) {\n          return fadeInElement(newEl, newSeris, newIndices[0]);\n        });\n      }\n    }\n    // else keep oldEl leaving animation.\n  }).updateManyToMany(function (newIndices, oldIndices) {\n    // If two data are same and both have groupId.\n    // Normally they should be diff by id.\n    new DataDiffer(oldIndices, newIndices, function (rawIdx) {\n      return oldDiffItems[rawIdx].data.getId(oldDiffItems[rawIdx].dataIndex);\n    }, function (rawIdx) {\n      return newDiffItems[rawIdx].data.getId(newDiffItems[rawIdx].dataIndex);\n    }).update(function (newIndex, oldIndex) {\n      // Use the original index\n      updateOneToOne(newIndices[newIndex], oldIndices[oldIndex]);\n    }).execute();\n  }).execute();\n  if (hasMorphAnimation) {\n    each(newList, function (_a) {\n      var data = _a.data;\n      var seriesModel = data.hostModel;\n      var view = seriesModel && api.getViewOfSeriesModel(seriesModel);\n      var animationCfg = getAnimationConfig('update', seriesModel, 0); // use 0 index.\n      if (view && seriesModel.isAnimationEnabled() && animationCfg && animationCfg.duration > 0) {\n        view.group.traverse(function (el) {\n          if (el instanceof Path && !el.animators.length) {\n            // We can't accept there still exists element that has no animation\n            // if universalTransition is enabled\n            el.animateFrom({\n              style: {\n                opacity: 0\n              }\n            }, animationCfg);\n          }\n        });\n      }\n    });\n  }\n}\nfunction getSeriesTransitionKey(series) {\n  var seriesKey = series.getModel('universalTransition').get('seriesKey');\n  if (!seriesKey) {\n    // Use series id by default.\n    return series.id;\n  }\n  return seriesKey;\n}\nfunction convertArraySeriesKeyToString(seriesKey) {\n  if (isArray(seriesKey)) {\n    // Order independent.\n    return seriesKey.sort().join(',');\n  }\n  return seriesKey;\n}\nfunction getDivideShapeFromData(data) {\n  if (data.hostModel) {\n    return data.hostModel.getModel('universalTransition').get('divideShape');\n  }\n}\nfunction findTransitionSeriesBatches(globalStore, params) {\n  var updateBatches = createHashMap();\n  var oldDataMap = createHashMap();\n  // Map that only store key in array seriesKey.\n  // Which is used to query the old data when transition from one to multiple series.\n  var oldDataMapForSplit = createHashMap();\n  each(globalStore.oldSeries, function (series, idx) {\n    var oldDataGroupId = globalStore.oldDataGroupIds[idx];\n    var oldData = globalStore.oldData[idx];\n    var transitionKey = getSeriesTransitionKey(series);\n    var transitionKeyStr = convertArraySeriesKeyToString(transitionKey);\n    oldDataMap.set(transitionKeyStr, {\n      dataGroupId: oldDataGroupId,\n      data: oldData\n    });\n    if (isArray(transitionKey)) {\n      // Same key can't in different array seriesKey.\n      each(transitionKey, function (key) {\n        oldDataMapForSplit.set(key, {\n          key: transitionKeyStr,\n          dataGroupId: oldDataGroupId,\n          data: oldData\n        });\n      });\n    }\n  });\n  function checkTransitionSeriesKeyDuplicated(transitionKeyStr) {\n    if (updateBatches.get(transitionKeyStr)) {\n      warn(\"Duplicated seriesKey in universalTransition \" + transitionKeyStr);\n    }\n  }\n  each(params.updatedSeries, function (series) {\n    if (series.isUniversalTransitionEnabled() && series.isAnimationEnabled()) {\n      var newDataGroupId = series.get('dataGroupId');\n      var newData = series.getData();\n      var transitionKey = getSeriesTransitionKey(series);\n      var transitionKeyStr = convertArraySeriesKeyToString(transitionKey);\n      // Only transition between series with same id.\n      var oldData = oldDataMap.get(transitionKeyStr);\n      // string transition key is the best match.\n      if (oldData) {\n        if (process.env.NODE_ENV !== 'production') {\n          checkTransitionSeriesKeyDuplicated(transitionKeyStr);\n        }\n        // TODO check if data is same?\n        updateBatches.set(transitionKeyStr, {\n          oldSeries: [{\n            dataGroupId: oldData.dataGroupId,\n            divide: getDivideShapeFromData(oldData.data),\n            data: oldData.data\n          }],\n          newSeries: [{\n            dataGroupId: newDataGroupId,\n            divide: getDivideShapeFromData(newData),\n            data: newData\n          }]\n        });\n      } else {\n        // Transition from multiple series.\n        // e.g. 'female', 'male' -> ['female', 'male']\n        if (isArray(transitionKey)) {\n          if (process.env.NODE_ENV !== 'production') {\n            checkTransitionSeriesKeyDuplicated(transitionKeyStr);\n          }\n          var oldSeries_1 = [];\n          each(transitionKey, function (key) {\n            var oldData = oldDataMap.get(key);\n            if (oldData.data) {\n              oldSeries_1.push({\n                dataGroupId: oldData.dataGroupId,\n                divide: getDivideShapeFromData(oldData.data),\n                data: oldData.data\n              });\n            }\n          });\n          if (oldSeries_1.length) {\n            updateBatches.set(transitionKeyStr, {\n              oldSeries: oldSeries_1,\n              newSeries: [{\n                dataGroupId: newDataGroupId,\n                data: newData,\n                divide: getDivideShapeFromData(newData)\n              }]\n            });\n          }\n        } else {\n          // Try transition to multiple series.\n          // e.g. ['female', 'male'] -> 'female', 'male'\n          var oldData_1 = oldDataMapForSplit.get(transitionKey);\n          if (oldData_1) {\n            var batch = updateBatches.get(oldData_1.key);\n            if (!batch) {\n              batch = {\n                oldSeries: [{\n                  dataGroupId: oldData_1.dataGroupId,\n                  data: oldData_1.data,\n                  divide: getDivideShapeFromData(oldData_1.data)\n                }],\n                newSeries: []\n              };\n              updateBatches.set(oldData_1.key, batch);\n            }\n            batch.newSeries.push({\n              dataGroupId: newDataGroupId,\n              data: newData,\n              divide: getDivideShapeFromData(newData)\n            });\n          }\n        }\n      }\n    }\n  });\n  return updateBatches;\n}\nfunction querySeries(series, finder) {\n  for (var i = 0; i < series.length; i++) {\n    var found = finder.seriesIndex != null && finder.seriesIndex === series[i].seriesIndex || finder.seriesId != null && finder.seriesId === series[i].id;\n    if (found) {\n      return i;\n    }\n  }\n}\nfunction transitionSeriesFromOpt(transitionOpt, globalStore, params, api) {\n  var from = [];\n  var to = [];\n  each(normalizeToArray(transitionOpt.from), function (finder) {\n    var idx = querySeries(globalStore.oldSeries, finder);\n    if (idx >= 0) {\n      from.push({\n        dataGroupId: globalStore.oldDataGroupIds[idx],\n        data: globalStore.oldData[idx],\n        // TODO can specify divideShape in transition.\n        divide: getDivideShapeFromData(globalStore.oldData[idx]),\n        groupIdDim: finder.dimension\n      });\n    }\n  });\n  each(normalizeToArray(transitionOpt.to), function (finder) {\n    var idx = querySeries(params.updatedSeries, finder);\n    if (idx >= 0) {\n      var data = params.updatedSeries[idx].getData();\n      to.push({\n        dataGroupId: globalStore.oldDataGroupIds[idx],\n        data: data,\n        divide: getDivideShapeFromData(data),\n        groupIdDim: finder.dimension\n      });\n    }\n  });\n  if (from.length > 0 && to.length > 0) {\n    transitionBetween(from, to, api);\n  }\n}\nexport function installUniversalTransition(registers) {\n  registers.registerUpdateLifecycle('series:beforeupdate', function (ecMOdel, api, params) {\n    each(normalizeToArray(params.seriesTransition), function (transOpt) {\n      each(normalizeToArray(transOpt.to), function (finder) {\n        var series = params.updatedSeries;\n        for (var i = 0; i < series.length; i++) {\n          if (finder.seriesIndex != null && finder.seriesIndex === series[i].seriesIndex || finder.seriesId != null && finder.seriesId === series[i].id) {\n            series[i][SERIES_UNIVERSAL_TRANSITION_PROP] = true;\n          }\n        }\n      });\n    });\n  });\n  registers.registerUpdateLifecycle('series:transition', function (ecModel, api, params) {\n    // TODO api provide an namespace that can save stuff per instance\n    var globalStore = getUniversalTransitionGlobalStore(api);\n    // TODO multiple to multiple series.\n    if (globalStore.oldSeries && params.updatedSeries && params.optionChanged) {\n      // TODO transitionOpt was used in an old implementation and can be removed now\n      // Use give transition config if its' give;\n      var transitionOpt = params.seriesTransition;\n      if (transitionOpt) {\n        each(normalizeToArray(transitionOpt), function (opt) {\n          transitionSeriesFromOpt(opt, globalStore, params, api);\n        });\n      } else {\n        // Else guess from series based on transition series key.\n        var updateBatches_1 = findTransitionSeriesBatches(globalStore, params);\n        each(updateBatches_1.keys(), function (key) {\n          var batch = updateBatches_1.get(key);\n          transitionBetween(batch.oldSeries, batch.newSeries, api);\n        });\n      }\n      // Reset\n      each(params.updatedSeries, function (series) {\n        // Reset;\n        if (series[SERIES_UNIVERSAL_TRANSITION_PROP]) {\n          series[SERIES_UNIVERSAL_TRANSITION_PROP] = false;\n        }\n      });\n    }\n    // Save all series of current update. Not only the updated one.\n    var allSeries = ecModel.getSeries();\n    var savedSeries = globalStore.oldSeries = [];\n    var savedDataGroupIds = globalStore.oldDataGroupIds = [];\n    var savedData = globalStore.oldData = [];\n    for (var i = 0; i < allSeries.length; i++) {\n      var data = allSeries[i].getData();\n      // Only save the data that can have transition.\n      // Avoid large data costing too much extra memory\n      if (data.count() < DATA_COUNT_THRESHOLD) {\n        savedSeries.push(allSeries[i]);\n        savedDataGroupIds.push(allSeries[i].get('dataGroupId'));\n        savedData.push(data);\n      }\n    }\n  });\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { use } from './lib/extension.js';\nexport * from './lib/export/core.js';\n// ----------------------------------------------\n// All of the modules that are allowed to be\n// imported are listed below.\n//\n// Users MUST NOT import other modules that are\n// not included in this list.\n// ----------------------------------------------\nimport { SVGRenderer, CanvasRenderer } from './lib/export/renderers.js';\nimport { LineChart, BarChart, PieChart, ScatterChart, RadarChart, MapChart, TreeChart, TreemapChart, GraphChart, GaugeChart, FunnelChart, ParallelChart, SankeyChart, BoxplotChart, CandlestickChart, EffectScatterChart, LinesChart, HeatmapChart, PictorialBarChart, ThemeRiverChart, SunburstChart, CustomChart } from './lib/export/charts.js';\nimport { GridComponent, PolarComponent, GeoComponent, SingleAxisComponent, ParallelComponent, CalendarComponent, GraphicComponent, ToolboxComponent, TooltipComponent, AxisPointerComponent, BrushComponent, TitleComponent, TimelineComponent, MarkPointComponent, MarkLineComponent, MarkAreaComponent, LegendComponent, DataZoomComponent, DataZoomInsideComponent, DataZoomSliderComponent, VisualMapComponent, VisualMapContinuousComponent, VisualMapPiecewiseComponent, AriaComponent, DatasetComponent, TransformComponent } from './lib/export/components.js';\nimport { UniversalTransition, LabelLayout } from './lib/export/features.js';\n// -----------------\n// Render engines\n// -----------------\n// Render via Canvas.\n// echarts.init(dom, null, { renderer: 'canvas' })\nuse([CanvasRenderer]);\n// Render via SVG.\n// echarts.init(dom, null, { renderer: 'svg' })\nuse([SVGRenderer]);\n// ----------------\n// Charts (series)\n// ----------------\n// All of the series types, for example:\n// chart.setOption({\n//     series: [{\n//         type: 'line' // or 'bar', 'pie', ...\n//     }]\n// });\nuse([LineChart, BarChart, PieChart, ScatterChart, RadarChart, MapChart, TreeChart, TreemapChart, GraphChart, GaugeChart, FunnelChart, ParallelChart, SankeyChart, BoxplotChart, CandlestickChart, EffectScatterChart, LinesChart, HeatmapChart, PictorialBarChart, ThemeRiverChart, SunburstChart, CustomChart]);\n// -------------------\n// Coordinate systems\n// -------------------\n// All of the axis modules have been included in the\n// coordinate system module below, do not need to\n// make extra import.\n// `cartesian` coordinate system. For some historical\n// reasons, it is named as grid, for example:\n// chart.setOption({\n//     grid: {...},\n//     xAxis: {...},\n//     yAxis: {...},\n//     series: [{...}]\n// });\nuse(GridComponent);\n// `polar` coordinate system, for example:\n// chart.setOption({\n//     polar: {...},\n//     radiusAxis: {...},\n//     angleAxis: {...},\n//     series: [{\n//         coordinateSystem: 'polar'\n//     }]\n// });\nuse(PolarComponent);\n// `geo` coordinate system, for example:\n// chart.setOption({\n//     geo: {...},\n//     series: [{\n//         coordinateSystem: 'geo'\n//     }]\n// });\nuse(GeoComponent);\n// `singleAxis` coordinate system (notice, it is a coordinate system\n// with only one axis, work for chart like theme river), for example:\n// chart.setOption({\n//     singleAxis: {...}\n//     series: [{type: 'themeRiver', ...}]\n// });\nuse(SingleAxisComponent);\n// `parallel` coordinate system, only work for parallel series, for example:\n// chart.setOption({\n//     parallel: {...},\n//     parallelAxis: [{...}, ...],\n//     series: [{\n//         type: 'parallel'\n//     }]\n// });\nuse(ParallelComponent);\n// `calendar` coordinate system. for example,\n// chart.setOption({\n//     calendar: {...},\n//     series: [{\n//         coordinateSystem: 'calendar'\n//     }]\n// );\nuse(CalendarComponent);\n// ------------------\n// Other components\n// ------------------\n// `graphic` component, for example:\n// chart.setOption({\n//     graphic: {...}\n// });\nuse(GraphicComponent);\n// `toolbox` component, for example:\n// chart.setOption({\n//     toolbox: {...}\n// });\nuse(ToolboxComponent);\n// `tooltip` component, for example:\n// chart.setOption({\n//     tooltip: {...}\n// });\nuse(TooltipComponent);\n// `axisPointer` component, for example:\n// chart.setOption({\n//     tooltip: {axisPointer: {...}, ...}\n// });\n// Or\n// chart.setOption({\n//     axisPointer: {...}\n// });\nuse(AxisPointerComponent);\n// `brush` component, for example:\n// chart.setOption({\n//     brush: {...}\n// });\n// Or\n// chart.setOption({\n//     tooltip: {feature: {brush: {...}}\n// })\nuse(BrushComponent);\n// `title` component, for example:\n// chart.setOption({\n//     title: {...}\n// });\nuse(TitleComponent);\n// `timeline` component, for example:\n// chart.setOption({\n//     timeline: {...}\n// });\nuse(TimelineComponent);\n// `markPoint` component, for example:\n// chart.setOption({\n//     series: [{markPoint: {...}}]\n// });\nuse(MarkPointComponent);\n// `markLine` component, for example:\n// chart.setOption({\n//     series: [{markLine: {...}}]\n// });\nuse(MarkLineComponent);\n// `markArea` component, for example:\n// chart.setOption({\n//     series: [{markArea: {...}}]\n// });\nuse(MarkAreaComponent);\n// `legend` component not scrollable. for example:\n// chart.setOption({\n//     legend: {...}\n// });\nuse(LegendComponent);\n// `dataZoom` component including both `dataZoomInside` and `dataZoomSlider`.\nuse(DataZoomComponent);\n// `dataZoom` component providing drag, pinch, wheel behaviors\n// inside coordinate system, for example:\n// chart.setOption({\n//     dataZoom: {type: 'inside'}\n// });\nuse(DataZoomInsideComponent);\n// `dataZoom` component providing a slider bar, for example:\n// chart.setOption({\n//     dataZoom: {type: 'slider'}\n// });\nuse(DataZoomSliderComponent);\n// `visualMap` component including both `visualMapContinuous` and `visualMapPiecewise`.\nuse(VisualMapComponent);\n// `visualMap` component providing continuous bar, for example:\n// chart.setOption({\n//     visualMap: {type: 'continuous'}\n// });\nuse(VisualMapContinuousComponent);\n// `visualMap` component providing pieces bar, for example:\n// chart.setOption({\n//     visualMap: {type: 'piecewise'}\n// });\nuse(VisualMapPiecewiseComponent);\n// `aria` component providing aria, for example:\n// chart.setOption({\n//     aria: {...}\n// });\nuse(AriaComponent);\n// dataset transform\n// chart.setOption({\n//     dataset: {\n//          transform: []\n//     }\n// });\nuse(TransformComponent);\nuse(DatasetComponent);\n// universal transition\n// chart.setOption({\n//     series: {\n//         universalTransition: { enabled: true }\n//     }\n// })\nuse(UniversalTransition);\n// label layout\n// chart.setOption({\n//     series: {\n//         labelLayout: { hideOverlap: true }\n//     }\n// })\nuse(LabelLayout);"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAI,MAAM,kBAAU;AACpB,SAAS,YAAY,GAAG,GAAG;AACvB,SAAO,KAAK,IAAI,IAAI,CAAC,IAAI;AAC7B;AACO,SAAS,mBAAmB,MAAM;AACrC,MAAI,OAAO,KAAK;AAChB,MAAI,MAAM,KAAK,IAAI;AACnB,MAAI,oBAAoB,CAAC;AACzB,MAAI;AACJ,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,WAAS,iBAAiB,GAAG,GAAG;AAC5B,QAAI,kBAAkB,eAAe,SAAS,GAAG;AAC7C,wBAAkB,KAAK,cAAc;AAAA,IACzC;AACA,qBAAiB,CAAC,GAAG,CAAC;AAAA,EAC1B;AACA,WAAS,QAAQA,KAAIC,KAAIC,KAAIC,KAAI;AAC7B,QAAI,EAAE,YAAYH,KAAIE,GAAE,KAAK,YAAYD,KAAIE,GAAE,IAAI;AAC/C,qBAAe,KAAKH,KAAIC,KAAIC,KAAIC,KAAID,KAAIC,GAAE;AAAA,IAC9C;AAAA,EACJ;AACA,WAAS,OAAOC,aAAYC,WAAUC,KAAIC,KAAIC,KAAIC,KAAI;AAClD,QAAI,QAAQ,KAAK,IAAIJ,YAAWD,WAAU;AAC1C,QAAIM,OAAM,KAAK,IAAI,QAAQ,CAAC,IAAI,IAAI;AACpC,QAAI,MAAML,YAAWD,cAAa,KAAK;AACvC,QAAI,KAAK,KAAK,IAAIA,WAAU;AAC5B,QAAI,KAAK,KAAK,IAAIA,WAAU;AAC5B,QAAI,KAAK,KAAK,IAAIC,SAAQ;AAC1B,QAAI,KAAK,KAAK,IAAIA,SAAQ;AAC1B,QAAIH,MAAK,KAAKM,MAAKF;AACnB,QAAIH,MAAK,KAAKM,MAAKF;AACnB,QAAI,KAAK,KAAKC,MAAKF;AACnB,QAAI,KAAK,KAAKG,MAAKF;AACnB,QAAI,KAAKC,MAAKE,OAAM;AACpB,QAAI,KAAKD,MAAKC,OAAM;AACpB,mBAAe,KAAKR,MAAK,KAAK,IAAIC,MAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE;AAAA,EACtF;AACA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,OAAM;AACtB,QAAI,MAAM,KAAK,GAAG;AAClB,QAAI,UAAU,MAAM;AACpB,QAAI,SAAS;AACT,WAAK,KAAK,CAAC;AACX,WAAK,KAAK,IAAI,CAAC;AACf,WAAK;AACL,WAAK;AACL,UAAI,QAAQ,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,IAAI,GAAG;AACjD,yBAAiB,CAAC,IAAI,EAAE;AAAA,MAC5B;AAAA,IACJ;AACA,YAAQ,KAAK;AAAA,MACT,KAAK,IAAI;AACL,aAAK,KAAK,KAAK,GAAG;AAClB,aAAK,KAAK,KAAK,GAAG;AAClB,yBAAiB,IAAI,EAAE;AACvB;AAAA,MACJ,KAAK,IAAI;AACL,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb,gBAAQ,IAAI,IAAI,IAAI,EAAE;AACtB,aAAK;AACL,aAAK;AACL;AAAA,MACJ,KAAK,IAAI;AACL,uBAAe,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,KAAK,GAAG,GAAG,KAAK,KAAK,GAAG,CAAC;AAC9F;AAAA,MACJ,KAAK,IAAI;AACL,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb,uBAAe,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,EAAE;AAC1H,aAAK;AACL,aAAK;AACL;AAAA,MACJ,KAAK,IAAI;AACL,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,aAAa,KAAK,GAAG;AACzB,YAAI,WAAW,KAAK,GAAG,IAAI;AAC3B,aAAK;AACL,YAAI,gBAAgB,CAAC,KAAK,GAAG;AAC7B,aAAK,KAAK,IAAI,UAAU,IAAI,KAAK;AACjC,aAAK,KAAK,IAAI,UAAU,IAAI,KAAK;AACjC,YAAI,SAAS;AACT,eAAK;AACL,eAAK;AACL,2BAAiB,IAAI,EAAE;AAAA,QAC3B,OACK;AACD,kBAAQ,IAAI,IAAI,IAAI,EAAE;AAAA,QAC1B;AACA,aAAK,KAAK,IAAI,QAAQ,IAAI,KAAK;AAC/B,aAAK,KAAK,IAAI,QAAQ,IAAI,KAAK;AAC/B,YAAI,QAAQ,gBAAgB,KAAK,KAAK,KAAK,KAAK;AAChD,iBAAS,QAAQ,YAAY,gBAAgB,QAAQ,WAAW,QAAQ,UAAU,SAAS,MAAM;AAC7F,cAAI,YAAY,gBAAgB,KAAK,IAAI,QAAQ,MAAM,QAAQ,IACzD,KAAK,IAAI,QAAQ,MAAM,QAAQ;AACrC,iBAAO,OAAO,WAAW,IAAI,IAAI,IAAI,EAAE;AAAA,QAC3C;AACA;AAAA,MACJ,KAAK,IAAI;AACL,aAAK,KAAK,KAAK,GAAG;AAClB,aAAK,KAAK,KAAK,GAAG;AAClB,aAAK,KAAK,KAAK,GAAG;AAClB,aAAK,KAAK,KAAK,GAAG;AAClB,yBAAiB,IAAI,EAAE;AACvB,gBAAQ,IAAI,IAAI,IAAI,EAAE;AACtB,gBAAQ,IAAI,IAAI,IAAI,EAAE;AACtB,gBAAQ,IAAI,IAAI,IAAI,EAAE;AACtB,gBAAQ,IAAI,IAAI,IAAI,EAAE;AACtB;AAAA,MACJ,KAAK,IAAI;AACL,0BAAkB,QAAQ,IAAI,IAAI,IAAI,EAAE;AACxC,aAAK;AACL,aAAK;AACL;AAAA,IACR;AAAA,EACJ;AACA,MAAI,kBAAkB,eAAe,SAAS,GAAG;AAC7C,sBAAkB,KAAK,cAAc;AAAA,EACzC;AACA,SAAO;AACX;AACA,SAAS,eAAe,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,OAAO;AAChE,MAAI,YAAY,IAAI,EAAE,KAAK,YAAY,IAAI,EAAE,KAAK,YAAY,IAAI,EAAE,KAAK,YAAY,IAAI,EAAE,GAAG;AAC1F,QAAI,KAAK,IAAI,EAAE;AACf;AAAA,EACJ;AACA,MAAI,iBAAiB,IAAI;AACzB,MAAI,qBAAqB,iBAAiB;AAC1C,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AACd,MAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACnC,QAAM;AACN,QAAM;AACN,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,KAAK;AACf,MAAI,YAAY,MAAM,MAAM,MAAM;AAClC,MAAI,YAAY,MAAM,MAAM,MAAM;AAClC,MAAI,YAAY,sBAAsB,YAAY,oBAAoB;AAClE,QAAI,KAAK,IAAI,EAAE;AACf;AAAA,EACJ;AACA,MAAI,WAAW,KAAK,MAAM,KAAK;AAC/B,MAAI,WAAW,CAAC,KAAK,MAAM,KAAK;AAChC,MAAI,QAAQ,YAAY,WAAW;AACnC,MAAI,QAAQ,YAAY,WAAW;AACnC,MAAI,QAAQ,sBAAsB,YAAY,KACvC,QAAQ,sBAAsB,YAAY,GAAG;AAChD,QAAI,KAAK,IAAI,EAAE;AACf;AAAA,EACJ;AACA,MAAI,UAAU,CAAC;AACf,MAAI,UAAU,CAAC;AACf,iBAAe,IAAI,IAAI,IAAI,IAAI,KAAK,OAAO;AAC3C,iBAAe,IAAI,IAAI,IAAI,IAAI,KAAK,OAAO;AAC3C,iBAAe,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,KAAK,KAAK;AACzH,iBAAe,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,KAAK,KAAK;AAC7H;AACO,SAAS,eAAe,MAAM,OAAO;AACxC,MAAI,oBAAoB,mBAAmB,IAAI;AAC/C,MAAI,WAAW,CAAC;AAChB,UAAQ,SAAS;AACjB,WAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AAC/C,QAAI,UAAU,kBAAkB,CAAC;AACjC,QAAI,UAAU,CAAC;AACf,QAAI,KAAK,QAAQ,CAAC;AAClB,QAAI,KAAK,QAAQ,CAAC;AAClB,YAAQ,KAAK,IAAI,EAAE;AACnB,aAAS,IAAI,GAAG,IAAI,QAAQ,UAAS;AACjC,UAAI,KAAK,QAAQ,GAAG;AACpB,UAAI,KAAK,QAAQ,GAAG;AACpB,UAAI,KAAK,QAAQ,GAAG;AACpB,UAAI,KAAK,QAAQ,GAAG;AACpB,UAAI,KAAK,QAAQ,GAAG;AACpB,UAAI,KAAK,QAAQ,GAAG;AACpB,qBAAe,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,KAAK;AAC7D,WAAK;AACL,WAAK;AAAA,IACT;AACA,aAAS,KAAK,OAAO;AAAA,EACzB;AACA,SAAO;AACX;;;AC3LA,SAAS,iBAAiB,SAAS,QAAQ,OAAO;AAC9C,MAAI,UAAU,QAAQ,MAAM;AAC5B,MAAI,aAAa,QAAQ,IAAI,MAAM;AACnC,MAAI,QAAQ,KAAK,IAAI,UAAU,UAAU;AACzC,MAAI,WAAW,KAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,CAAC;AACjD,MAAI,cAAc,KAAK,MAAM,QAAQ,QAAQ;AAC7C,MAAI,gBAAgB,GAAG;AACnB,kBAAc;AACd,eAAW;AAAA,EACf;AACA,MAAI,QAAQ,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,UAAM,KAAK,WAAW;AAAA,EAC1B;AACA,MAAI,eAAe,WAAW;AAC9B,MAAI,WAAW,QAAQ;AACvB,MAAI,WAAW,GAAG;AACd,aAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,YAAM,IAAI,QAAQ,KAAK;AAAA,IAC3B;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,aAAa,aAAa,OAAO,WAAW;AACjD,MAAI,KAAK,YAAY;AACrB,MAAI,IAAI,YAAY;AACpB,MAAI,aAAa,YAAY;AAC7B,MAAI,WAAW,YAAY;AAC3B,MAAI,QAAQ,KAAK,IAAI,WAAW,UAAU;AAC1C,MAAI,SAAS,QAAQ;AACrB,MAAI,SAAS,IAAI;AACjB,MAAI,aAAa,SAAS,KAAK,IAAI,MAAM;AACzC,MAAI,QAAQ,iBAAiB,CAAC,QAAQ,MAAM,GAAG,aAAa,IAAI,GAAG,KAAK;AACxE,MAAI,WAAW,aAAa,QAAQ,UAAU,MAAM;AACpD,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO;AACzC,QAAI,cAAc,aAAa,SAAS,SAAS,MAAM,GAAG;AAC1D,aAAS,SAAS,GAAG,SAAS,MAAM,GAAG,GAAG,UAAU;AAChD,UAAI,WAAW,CAAC;AAChB,UAAI,YAAY;AACZ,iBAAS,aAAa,aAAa,UAAU;AAC7C,iBAAS,WAAW,aAAa,WAAW,MAAM;AAClD,iBAAS,KAAK,KAAK,aAAa;AAChC,iBAAS,IAAI,KAAK,cAAc,SAAS;AAAA,MAC7C,OACK;AACD,iBAAS,aAAa,aAAa,aAAa;AAChD,iBAAS,WAAW,aAAa,cAAc,SAAS;AACxD,iBAAS,KAAK,KAAK,UAAU;AAC7B,iBAAS,IAAI,KAAK,WAAW,MAAM;AAAA,MACvC;AACA,eAAS,YAAY,YAAY;AACjC,eAAS,KAAK,YAAY;AAC1B,eAAS,KAAK,YAAY;AAC1B,gBAAU,KAAK,QAAQ;AAAA,IAC3B;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,WAAW,OAAO,WAAW;AAC7C,MAAI,QAAQ,UAAU;AACtB,MAAI,SAAS,UAAU;AACvB,MAAI,kBAAkB,QAAQ;AAC9B,MAAI,QAAQ,iBAAiB,CAAC,OAAO,MAAM,GAAG,kBAAkB,IAAI,GAAG,KAAK;AAC5E,MAAI,aAAa,kBAAkB,UAAU;AAC7C,MAAI,gBAAgB,kBAAkB,WAAW;AACjD,MAAI,SAAS,kBAAkB,MAAM;AACrC,MAAI,YAAY,kBAAkB,MAAM;AACxC,MAAI,UAAU,UAAU,UAAU,IAAI,MAAM;AAC5C,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO;AACzC,QAAI,aAAa,UAAU,aAAa,IAAI,MAAM,GAAG;AACrD,aAAS,SAAS,GAAG,SAAS,MAAM,GAAG,GAAG,UAAU;AAChD,UAAI,WAAW,CAAC;AAChB,eAAS,MAAM,IAAI,MAAM;AACzB,eAAS,SAAS,IAAI,SAAS;AAC/B,eAAS,UAAU,IAAI;AACvB,eAAS,aAAa,IAAI;AAC1B,eAAS,KAAK,UAAU;AACxB,eAAS,KAAK,UAAU;AACxB,gBAAU,KAAK,QAAQ;AAAA,IAC3B;AAAA,EACJ;AACJ;AACA,SAAS,eAAe,IAAI,IAAI,IAAI,IAAI;AACpC,SAAO,KAAK,KAAK,KAAK;AAC1B;AACA,SAAS,kBAAkB,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC/D,MAAI,KAAK,MAAM;AACf,MAAI,KAAK,MAAM;AACf,MAAI,KAAK,MAAM;AACf,MAAI,KAAK,MAAM;AACf,MAAI,iBAAiB,eAAe,IAAI,IAAI,IAAI,EAAE;AAClD,MAAI,KAAK,IAAI,cAAc,IAAI,MAAM;AACjC,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,MAAM;AAClB,MAAI,QAAQ,MAAM;AAClB,MAAI,IAAI,eAAe,OAAO,OAAO,IAAI,EAAE,IAAI;AAC/C,MAAI,IAAI,KAAK,IAAI,GAAG;AAChB,WAAO;AAAA,EACX;AACA,SAAO,IAAI,cAAM,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG;AAC/C;AACA,SAAS,aAAa,IAAI,OAAO,OAAO;AACpC,MAAI,MAAM,IAAI,cAAM;AACpB,gBAAM,IAAI,KAAK,OAAO,KAAK;AAC3B,MAAI,UAAU;AACd,MAAI,OAAO,IAAI,cAAM;AACrB,gBAAM,IAAI,MAAM,IAAI,KAAK;AACzB,MAAI,MAAM,KAAK,IAAI,GAAG;AACtB,SAAO;AACX;AACA,SAAS,UAAU,MAAM,IAAI;AACzB,MAAI,OAAO,KAAK,KAAK,SAAS,CAAC;AAC/B,MAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG;AAChD;AAAA,EACJ;AACA,OAAK,KAAK,EAAE;AAChB;AACA,SAAS,mBAAmB,QAAQ,OAAO,OAAO;AAC9C,MAAI,MAAM,OAAO;AACjB,MAAI,gBAAgB,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,QAAI,KAAK,OAAO,CAAC;AACjB,QAAI,KAAK,QAAQ,IAAI,KAAK,GAAG;AAC7B,QAAI,iBAAiB,kBAAkB,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACrG,QAAI,gBAAgB;AAChB,oBAAc,KAAK;AAAA,QACf,QAAQ,aAAa,gBAAgB,OAAO,KAAK;AAAA,QACjD,IAAI;AAAA,QACJ,KAAK;AAAA,MACT,CAAC;AAAA,IACL;AAAA,EACJ;AACA,MAAI,cAAc,SAAS,GAAG;AAC1B,WAAO,CAAC,EAAE,OAAe,GAAG,EAAE,OAAe,CAAC;AAAA,EAClD;AACA,gBAAc,KAAK,SAAU,GAAG,GAAG;AAC/B,WAAO,EAAE,SAAS,EAAE;AAAA,EACxB,CAAC;AACD,MAAI,WAAW,cAAc,CAAC;AAC9B,MAAI,WAAW,cAAc,cAAc,SAAS,CAAC;AACrD,MAAI,SAAS,MAAM,SAAS,KAAK;AAC7B,QAAI,MAAM;AACV,eAAW;AACX,eAAW;AAAA,EACf;AACA,MAAI,cAAc,CAAC,SAAS,GAAG,GAAG,SAAS,GAAG,CAAC;AAC/C,MAAI,cAAc,CAAC,SAAS,GAAG,GAAG,SAAS,GAAG,CAAC;AAC/C,MAAI,WAAW,CAAC,WAAW;AAC3B,MAAI,WAAW,CAAC,WAAW;AAC3B,WAAS,IAAI,SAAS,MAAM,GAAG,KAAK,SAAS,KAAK,KAAK;AACnD,cAAU,UAAU,OAAO,CAAC,EAAE,MAAM,CAAC;AAAA,EACzC;AACA,YAAU,UAAU,WAAW;AAC/B,YAAU,UAAU,WAAW;AAC/B,WAAS,IAAI,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,KAAK,KAAK;AACzD,cAAU,UAAU,OAAO,IAAI,GAAG,EAAE,MAAM,CAAC;AAAA,EAC/C;AACA,YAAU,UAAU,WAAW;AAC/B,YAAU,UAAU,WAAW;AAC/B,SAAO,CAAC;AAAA,IACA,QAAQ;AAAA,EACZ,GAAG;AAAA,IACC,QAAQ;AAAA,EACZ,CAAC;AACT;AACA,SAAS,oBAAoB,cAAc;AACvC,MAAI,SAAS,aAAa;AAC1B,MAAI,MAAM,CAAC;AACX,MAAI,MAAM,CAAC;AACX,aAAW,QAAQ,KAAK,GAAG;AAC3B,MAAI,eAAe,IAAI,qBAAa,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;AACpF,MAAI,QAAQ,aAAa;AACzB,MAAI,SAAS,aAAa;AAC1B,MAAI,IAAI,aAAa;AACrB,MAAI,IAAI,aAAa;AACrB,MAAI,MAAM,IAAI,cAAM;AACpB,MAAI,MAAM,IAAI,cAAM;AACpB,MAAI,QAAQ,QAAQ;AAChB,QAAI,IAAI,IAAI,IAAI,IAAI,QAAQ;AAC5B,QAAI,IAAI;AACR,QAAI,IAAI,IAAI;AAAA,EAChB,OACK;AACD,QAAI,IAAI,IAAI,IAAI,IAAI,SAAS;AAC7B,QAAI,IAAI;AACR,QAAI,IAAI,IAAI;AAAA,EAChB;AACA,SAAO,mBAAmB,QAAQ,KAAK,GAAG;AAC9C;AACA,SAAS,sBAAsB,SAAS,OAAO,OAAO,KAAK;AACvD,MAAI,UAAU,GAAG;AACb,QAAI,KAAK,KAAK;AAAA,EAClB,OACK;AACD,QAAI,MAAM,KAAK,MAAM,QAAQ,CAAC;AAC9B,QAAI,MAAM,QAAQ,KAAK;AACvB,0BAAsB,SAAS,IAAI,CAAC,GAAG,KAAK,GAAG;AAC/C,0BAAsB,SAAS,IAAI,CAAC,GAAG,QAAQ,KAAK,GAAG;AAAA,EAC3D;AACA,SAAO;AACX;AACO,SAAS,MAAM,MAAM,OAAO;AAC/B,MAAI,QAAQ,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,UAAM,KAAK,UAAU,IAAI,CAAC;AAAA,EAC9B;AACA,SAAO;AACX;AACA,SAAS,cAAc,QAAQ,QAAQ;AACnC,SAAO,SAAS,OAAO,KAAK;AAC5B,SAAO,IAAI,OAAO;AAClB,SAAO,KAAK,OAAO;AACnB,SAAO,SAAS,OAAO;AAC3B;AACA,SAAS,eAAe,QAAQ;AAC5B,MAAI,MAAM,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,OAAO,UAAS;AAChC,QAAI,KAAK,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC,CAAC;AAAA,EACvC;AACA,SAAO;AACX;AACO,SAAS,MAAM,MAAM,OAAO;AAC/B,MAAI,YAAY,CAAC;AACjB,MAAI,QAAQ,KAAK;AACjB,MAAI;AACJ,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK;AACD,iBAAW,OAAO,OAAO,SAAS;AAClC,qBAAe;AACf;AAAA,IACJ,KAAK;AACD,mBAAa,OAAO,OAAO,SAAS;AACpC,qBAAe;AACf;AAAA,IACJ,KAAK;AACD,mBAAa;AAAA,QACT,IAAI;AAAA,QAAG,GAAG,MAAM;AAAA,QAAG,YAAY;AAAA,QAAG,UAAU,KAAK,KAAK;AAAA,QACtD,IAAI,MAAM;AAAA,QAAI,IAAI,MAAM;AAAA,MAC5B,GAAG,OAAO,SAAS;AACnB,qBAAe;AACf;AAAA,IACJ;AACI,UAAI,IAAI,KAAK,qBAAqB;AAClC,UAAI,QAAQ,IAAI,KAAK,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI;AAC5F,UAAI,WAAW,IAAI,eAAe,KAAK,oBAAoB,GAAG,KAAK,GAAG,SAAU,MAAM;AAAE,eAAO,eAAe,IAAI;AAAA,MAAG,CAAC;AACtH,UAAI,eAAe,SAAS;AAC5B,UAAI,iBAAiB,GAAG;AACpB,8BAAsB,qBAAqB;AAAA,UACvC,QAAQ,SAAS,CAAC;AAAA,QACtB,GAAG,OAAO,SAAS;AAAA,MACvB,WACS,iBAAiB,OAAO;AAC7B,iBAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACnC,oBAAU,KAAK;AAAA,YACX,QAAQ,SAAS,CAAC;AAAA,UACtB,CAAC;AAAA,QACL;AAAA,MACJ,OACK;AACD,YAAI,cAAc;AAClB,YAAI,QAAQ,IAAI,UAAU,SAAU,MAAM;AACtC,cAAI,MAAM,CAAC;AACX,cAAI,MAAM,CAAC;AACX,qBAAW,MAAM,KAAK,GAAG;AACzB,cAAI,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9C,yBAAe;AACf,iBAAO,EAAE,MAAY,KAAW;AAAA,QACpC,CAAC;AACD,cAAM,KAAK,SAAU,GAAG,GAAG;AAAE,iBAAO,EAAE,OAAO,EAAE;AAAA,QAAM,CAAC;AACtD,YAAI,OAAO;AACX,iBAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACnC,cAAI,OAAO,MAAM,CAAC;AAClB,cAAI,QAAQ,GAAG;AACX;AAAA,UACJ;AACA,cAAI,YAAY,MAAM,eAAe,IAC/B,OACA,KAAK,KAAK,KAAK,OAAO,cAAc,KAAK;AAC/C,cAAI,YAAY,GAAG;AACf;AAAA,UACJ;AACA,gCAAsB,qBAAqB;AAAA,YACvC,QAAQ,KAAK;AAAA,UACjB,GAAG,WAAW,SAAS;AACvB,kBAAQ;AAAA,QACZ;AACA;AAAA,MACJ;AACA,qBAAe;AACf;AAAA,EACR;AACA,MAAI,CAAC,cAAc;AACf,WAAO,MAAM,MAAM,KAAK;AAAA,EAC5B;AACA,MAAI,MAAM,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,UAAU,IAAI,aAAa;AAC/B,YAAQ,SAAS,UAAU,CAAC,CAAC;AAC7B,kBAAc,MAAM,OAAO;AAC3B,QAAI,KAAK,OAAO;AAAA,EACpB;AACA,SAAO;AACX;;;AC/SA,SAAS,aAAa,UAAU,UAAU;AACtC,MAAI,OAAO,SAAS;AACpB,MAAI,OAAO,SAAS;AACpB,MAAI,SAAS,MAAM;AACf,WAAO,CAAC,UAAU,QAAQ;AAAA,EAC9B;AACA,MAAI,UAAU,CAAC;AACf,MAAI,UAAU,CAAC;AACf,MAAI,cAAc,OAAO,OAAO,WAAW;AAC3C,MAAI,aAAa,KAAK,IAAI,MAAM,IAAI;AACpC,MAAI,OAAO,KAAK,IAAI,OAAO,IAAI,IAAI;AACnC,MAAI,sBAAsB,aAAa,KAAK;AAC5C,MAAI,uBAAuB,KAAK,KAAK,OAAO,kBAAkB,IAAI;AAClE,MAAI,aAAa,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAChD,MAAI,WAAW;AACf,WAAS,IAAI,GAAG,IAAI,cAAa;AAC7B,QAAI,KAAK,YAAY,IAAI,CAAC;AAC1B,QAAI,KAAK,YAAY,IAAI,CAAC;AAC1B,QAAI,KAAK,YAAY,GAAG;AACxB,QAAI,KAAK,YAAY,GAAG;AACxB,QAAI,KAAK,YAAY,GAAG;AACxB,QAAI,KAAK,YAAY,GAAG;AACxB,QAAI,KAAK,YAAY,GAAG;AACxB,QAAI,KAAK,YAAY,GAAG;AACxB,QAAI,YAAY,GAAG;AACf,iBAAW,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACtC;AAAA,IACJ;AACA,QAAI,oBAAoB,KAAK,IAAI,UAAU,uBAAuB,CAAC,IAAI;AACvE,aAAS,IAAI,GAAG,KAAK,mBAAmB,KAAK;AACzC,UAAI,IAAI,IAAI;AACZ,qBAAe,IAAI,IAAI,IAAI,IAAI,GAAG,OAAO;AACzC,qBAAe,IAAI,IAAI,IAAI,IAAI,GAAG,OAAO;AACzC,WAAK,QAAQ,CAAC;AACd,WAAK,QAAQ,CAAC;AACd,iBAAW,KAAK,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,IAAI,EAAE;AACtE,WAAK,QAAQ,CAAC;AACd,WAAK,QAAQ,CAAC;AACd,WAAK,QAAQ,CAAC;AACd,WAAK,QAAQ,CAAC;AAAA,IAClB;AACA,gBAAY,oBAAoB;AAAA,EACpC;AACA,SAAO,gBAAgB,WAAW,CAAC,YAAY,QAAQ,IAAI,CAAC,UAAU,UAAU;AACpF;AACA,SAAS,cAAc,oBAAoB,cAAc;AACrD,MAAI,MAAM,mBAAmB;AAC7B,MAAI,QAAQ,mBAAmB,MAAM,CAAC;AACtC,MAAI,QAAQ,mBAAmB,MAAM,CAAC;AACtC,MAAI,aAAa,CAAC;AAClB,WAAS,IAAI,GAAG,IAAI,aAAa,UAAS;AACtC,eAAW,GAAG,IAAI;AAClB,eAAW,GAAG,IAAI;AAAA,EACtB;AACA,SAAO;AACX;AACO,SAAS,kBAAkB,QAAQ,QAAQ;AAC9C,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,YAAY,CAAC;AACjB,MAAI,YAAY,CAAC;AACjB,WAAS,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,QAAQ,OAAO,MAAM,GAAG,KAAK;AAC7D,QAAI,WAAW,OAAO,CAAC;AACvB,QAAI,WAAW,OAAO,CAAC;AACvB,QAAI,cAAc;AAClB,QAAI,cAAc;AAClB,QAAI,CAAC,UAAU;AACX,oBAAc,cAAc,gBAAgB,UAAU,QAAQ;AAC9D,oBAAc;AAAA,IAClB,WACS,CAAC,UAAU;AAChB,oBAAc,cAAc,gBAAgB,UAAU,QAAQ;AAC9D,oBAAc;AAAA,IAClB,OACK;AACD,WAAK,aAAa,UAAU,QAAQ,GAAG,cAAc,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;AAC9E,qBAAe;AACf,qBAAe;AAAA,IACnB;AACA,cAAU,KAAK,WAAW;AAC1B,cAAU,KAAK,WAAW;AAAA,EAC9B;AACA,SAAO,CAAC,WAAW,SAAS;AAChC;AACO,SAAS,SAAS,OAAO;AAC5B,MAAI,aAAa;AACjB,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,MAAM,MAAM;AAChB,WAAS,IAAI,GAAG,IAAI,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG;AACjD,QAAI,KAAK,MAAM,CAAC;AAChB,QAAI,KAAK,MAAM,IAAI,CAAC;AACpB,QAAI,KAAK,MAAM,CAAC;AAChB,QAAI,KAAK,MAAM,IAAI,CAAC;AACpB,QAAI,IAAI,KAAK,KAAK,KAAK;AACvB,kBAAc;AACd,WAAO,KAAK,MAAM;AAClB,WAAO,KAAK,MAAM;AAAA,EACtB;AACA,MAAI,eAAe,GAAG;AAClB,WAAO,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AAAA,EACxC;AACA,SAAO,CAAC,KAAK,aAAa,GAAG,KAAK,aAAa,GAAG,UAAU;AAChE;AACA,SAAS,mBAAmB,gBAAgB,cAAc,QAAQ,MAAM;AACpE,MAAI,eAAe,eAAe,SAAS,KAAK;AAChD,MAAI,YAAY;AAChB,MAAI,aAAa;AACjB,MAAI,MAAM,eAAe;AACzB,MAAI,OAAO,MAAM;AACjB,WAAS,SAAS,GAAG,SAAS,aAAa,UAAU;AACjD,QAAI,eAAe,SAAS;AAC5B,QAAI,QAAQ;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC7B,UAAI,MAAM,MAAM,IAAI,gBAAiB,eAAe,IAAI,KAAK,OAAO;AACpE,UAAI,KAAK,eAAe,GAAG,IAAI,OAAO,CAAC;AACvC,UAAI,KAAK,eAAe,MAAM,CAAC,IAAI,OAAO,CAAC;AAC3C,UAAI,KAAK,aAAa,CAAC,IAAI,KAAK,CAAC;AACjC,UAAI,KAAK,aAAa,IAAI,CAAC,IAAI,KAAK,CAAC;AACrC,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,KAAK;AACd,eAAS,KAAK,KAAK,KAAK;AAAA,IAC5B;AACA,QAAI,QAAQ,WAAW;AACnB,kBAAY;AACZ,mBAAa;AAAA,IACjB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,QAAQ,OAAO;AACpB,MAAI,SAAS,CAAC;AACd,MAAI,MAAM,MAAM;AAChB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC7B,WAAO,CAAC,IAAI,MAAM,MAAM,IAAI,CAAC;AAC7B,WAAO,IAAI,CAAC,IAAI,MAAM,MAAM,IAAI,CAAC;AAAA,EACrC;AACA,SAAO;AACX;AACA,SAAS,yBAAyB,SAAS,OAAO,sBAAsB,kBAAkB;AACtF,MAAI,SAAS,CAAC;AACd,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,QAAI,oBAAoB,QAAQ,CAAC;AACjC,QAAI,kBAAkB,MAAM,CAAC;AAC7B,QAAI,SAAS,SAAS,iBAAiB;AACvC,QAAI,OAAO,SAAS,eAAe;AACnC,QAAI,oBAAoB,MAAM;AAC1B,yBAAmB,OAAO,CAAC,IAAI,MAAM,KAAK,CAAC,IAAI;AAAA,IACnD;AACA,QAAI,uBAAuB,CAAC;AAC5B,QAAI,qBAAqB,CAAC;AAC1B,QAAI,YAAY;AAChB,QAAI,YAAY;AAChB,QAAI,SAAS,CAAC;AACd,QAAI,MAAM,kBAAkB;AAC5B,QAAI,kBAAkB;AAClB,0BAAoB,QAAQ,iBAAiB;AAAA,IACjD;AACA,QAAI,SAAS,mBAAmB,mBAAmB,iBAAiB,QAAQ,IAAI,IAAI;AACpF,QAAI,OAAO,MAAM;AACjB,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG;AAC9B,UAAI,OAAO,SAAS,KAAK,OAAO;AAChC,2BAAqB,IAAI,CAAC,IAAI,kBAAkB,GAAG,IAAI,OAAO,CAAC;AAC/D,2BAAqB,IAAI,CAAC,IAAI,kBAAkB,MAAM,CAAC,IAAI,OAAO,CAAC;AAAA,IACvE;AACA,yBAAqB,CAAC,IAAI,kBAAkB,MAAM,IAAI,OAAO,CAAC;AAC9D,yBAAqB,CAAC,IAAI,kBAAkB,SAAS,CAAC,IAAI,OAAO,CAAC;AAClE,QAAI,uBAAuB,GAAG;AAC1B,UAAI,OAAO,mBAAmB;AAC9B,eAAS,QAAQ,CAAC,mBAAmB,GAAG,SAAS,mBAAmB,GAAG,SAAS,MAAM;AAClF,YAAI,KAAK,KAAK,IAAI,KAAK;AACvB,YAAI,KAAK,KAAK,IAAI,KAAK;AACvB,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK,GAAG;AAClD,cAAI,KAAK,qBAAqB,CAAC;AAC/B,cAAI,KAAK,qBAAqB,IAAI,CAAC;AACnC,cAAI,KAAK,gBAAgB,CAAC,IAAI,KAAK,CAAC;AACpC,cAAI,KAAK,gBAAgB,IAAI,CAAC,IAAI,KAAK,CAAC;AACxC,cAAI,QAAQ,KAAK,KAAK,KAAK;AAC3B,cAAI,QAAQ,KAAK,KAAK,KAAK;AAC3B,iBAAO,CAAC,IAAI;AACZ,iBAAO,IAAI,CAAC,IAAI;AAChB,cAAI,KAAK,QAAQ;AACjB,cAAI,KAAK,QAAQ;AACjB,mBAAS,KAAK,KAAK,KAAK;AAAA,QAC5B;AACA,YAAI,QAAQ,WAAW;AACnB,sBAAY;AACZ,sBAAY;AACZ,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,+BAAmB,CAAC,IAAI,OAAO,CAAC;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,OACK;AACD,eAAS,MAAM,GAAG,MAAM,KAAK,OAAO,GAAG;AACnC,2BAAmB,GAAG,IAAI,gBAAgB,GAAG,IAAI,KAAK,CAAC;AACvD,2BAAmB,MAAM,CAAC,IAAI,gBAAgB,MAAM,CAAC,IAAI,KAAK,CAAC;AAAA,MACnE;AAAA,IACJ;AACA,WAAO,KAAK;AAAA,MACR,MAAM;AAAA,MACN,IAAI;AAAA,MACJ;AAAA,MACA;AAAA,MACA,UAAU,CAAC;AAAA,IACf,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACO,SAAS,kBAAkB,MAAM;AACpC,SAAO,KAAK;AAChB;AAIA,IAAI,sBAAsB;AAC1B,SAAS,oBAAoB,KAAK,YAAY,WAAW;AACrD,MAAI,kBAAkB,sBAAsB;AAC5C,MAAI,iBAAiB,IAAI,eAAe,KAAK,IAAI,UAAU;AAC3D,MAAI,CAAC,IAAI,eAAe,GAAG;AACvB,QAAI,eAAe,IAAI,IAAI,UAAU;AAAA,EACzC;AACA,MAAI,UAAU,UAAU;AACxB,MAAI,QAAQ,UAAU;AACtB,MAAI,SAAS,UAAU;AACvB,MAAI,UAAU,IAAI,WAAY;AAC1B,QAAI,OAAO;AACX,QAAI;AACJ,cAAU,OAAO,MAAM,MAAM,IAAI;AACjC,QAAI,SAAS;AACT,YAAM,QAAQ,MAAM,MAAM,IAAI;AAAA,IAClC,OACK;AACD,YAAM,eAAe,MAAM,MAAM,IAAI;AAAA,IACzC;AACA,aAAS,MAAM,MAAM,MAAM,IAAI;AAC/B,WAAO;AAAA,EACX;AACJ;AACA,SAAS,cAAc,KAAK,YAAY;AACpC,MAAI,kBAAkB,sBAAsB;AAC5C,MAAI,IAAI,eAAe,GAAG;AACtB,QAAI,UAAU,IAAI,IAAI,eAAe;AACrC,QAAI,eAAe,IAAI;AAAA,EAC3B;AACJ;AACA,SAAS,wBAAwB,cAAc,IAAI;AAC/C,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,QAAI,aAAa,aAAa,CAAC;AAC/B,aAAS,IAAI,GAAG,IAAI,WAAW,UAAS;AACpC,UAAI,IAAI,WAAW,CAAC;AACpB,UAAI,IAAI,WAAW,IAAI,CAAC;AACxB,iBAAW,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC;AAC9C,iBAAW,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC;AAAA,IAClD;AAAA,EACJ;AACJ;AACA,SAAS,iBAAiB,UAAU,QAAQ;AACxC,MAAI,gBAAgB,SAAS,oBAAoB;AACjD,MAAI,cAAc,OAAO,oBAAoB;AAC7C,MAAI,KAAK,kBAAkB,mBAAmB,aAAa,GAAG,mBAAmB,WAAW,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AAC/I,MAAI,oBAAoB,SAAS,qBAAqB;AACtD,MAAI,kBAAkB,OAAO,qBAAqB;AAClD,WAAS,0BAA0B;AAC/B,SAAK,YAAY;AAAA,EACrB;AACA,uBAAqB,wBAAwB,kBAAkB,iBAAiB;AAChF,qBAAmB,wBAAwB,gBAAgB,eAAe;AAC1E,sBAAoB,QAAQ,mBAAmB,EAAE,SAAS,wBAAwB,CAAC;AACnF,SAAO,YAAY;AACnB,MAAI,eAAe,yBAAyB,kBAAkB,gBAAgB,IAAI,KAAK,EAAE;AACzF,MAAI,SAAS,CAAC;AACd,sBAAoB,QAAQ,aAAa,EAAE,SAAS,SAAU,MAAM;AAC5D,QAAI,IAAI,OAAO;AACf,QAAI,OAAO,IAAI;AACf,QAAI,QAAQ,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,UAAI,OAAO,aAAa,CAAC;AACzB,UAAI,OAAO,KAAK;AAChB,UAAI,KAAK,KAAK;AACd,UAAI,QAAQ,KAAK,WAAW;AAC5B,UAAI,SAAS,KAAK;AAClB,UAAI,OAAO,KAAK;AAChB,UAAI,KAAK,KAAK,IAAI,KAAK;AACvB,UAAI,KAAK,KAAK,IAAI,KAAK;AACvB,WAAK,OAAO,QAAQ,MAAM,CAAC;AAC3B,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACrC,YAAI,OAAO,KAAK,CAAC;AACjB,YAAI,OAAO,KAAK,IAAI,CAAC;AACrB,YAAI,KAAK,GAAG,CAAC;AACb,YAAI,KAAK,GAAG,IAAI,CAAC;AACjB,YAAI,IAAI,OAAO,OAAO,KAAK;AAC3B,YAAI,IAAI,OAAO,OAAO,KAAK;AAC3B,eAAO,CAAC,IAAK,IAAI,KAAK,IAAI,KAAM,MAAM,CAAC;AACvC,eAAO,IAAI,CAAC,IAAK,IAAI,KAAK,IAAI,KAAM,MAAM,CAAC;AAAA,MAC/C;AACA,UAAI,KAAK,OAAO,CAAC;AACjB,UAAI,KAAK,OAAO,CAAC;AACjB,WAAK,OAAO,IAAI,EAAE;AAClB,eAAS,IAAI,GAAG,IAAI,KAAK,UAAS;AAC9B,YAAI,KAAK,OAAO,GAAG;AACnB,YAAI,KAAK,OAAO,GAAG;AACnB,YAAI,KAAK,OAAO,GAAG;AACnB,YAAI,KAAK,OAAO,GAAG;AACnB,YAAI,KAAK,OAAO,GAAG;AACnB,YAAI,KAAK,OAAO,GAAG;AACnB,YAAI,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,IAAI;AAClD,eAAK,OAAO,IAAI,EAAE;AAAA,QACtB,OACK;AACD,eAAK,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,QAC7C;AACA,aAAK;AACL,aAAK;AAAA,MACT;AAAA,IACJ;AAAA,EACJ,EAAE,CAAC;AACX;AACO,SAAS,UAAU,UAAU,QAAQ,eAAe;AACvD,MAAI,CAAC,YAAY,CAAC,QAAQ;AACtB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,cAAc;AAC5B,MAAI,YAAY,cAAc;AAC9B,mBAAiB,UAAU,MAAM;AACjC,SAAO,WAAW;AAClB,WAAS,gBAAgB;AACrB,kBAAc,QAAQ,WAAW;AACjC,kBAAc,QAAQ,iBAAiB;AACvC,WAAO,WAAW;AAClB,WAAO,gBAAgB;AACvB,WAAO,WAAW;AAAA,EACtB;AACA,SAAO,UAAU;AAAA,IACb,UAAU;AAAA,EACd,GAAG,SAAS;AAAA,IACR,QAAQ,SAAU,GAAG;AACjB,aAAO,WAAW;AAClB,mBAAa,UAAU,CAAC;AAAA,IAC5B;AAAA,IACA,MAAM,WAAY;AACd,oBAAc;AACd,iBAAW,QAAQ;AAAA,IACvB;AAAA,EACJ,GAAG,aAAa,CAAC;AACjB,SAAO;AACX;AACA,SAAS,QAAQ,GAAG,GAAG,MAAM,MAAM,MAAM,MAAM;AAC3C,MAAI,OAAO;AACX,MAAK,SAAS,OAAQ,IAAI,KAAK,MAAM,SAAS,IAAI,SAAS,OAAO,KAAK;AACvE,MAAK,SAAS,OAAQ,IAAI,KAAK,MAAM,SAAS,IAAI,SAAS,OAAO,KAAK;AACvE,MAAI,IAAI;AACR,MAAI;AACJ,WAAS,KAAK,KAAK,QAAQ,GAAG,IAAI,GAAG,KAAK,GAAG;AACzC,QAAI,KAAK;AACT,QAAI,KAAK;AACT,SAAK,IAAI,KAAK,GAAG;AACb,WAAK;AAAA,IACT;AACA,SAAK,IAAI,KAAK,GAAG;AACb,WAAK;AAAA,IACT;AACA,SAAK,IAAI,KAAM,IAAI,KAAM;AACzB,QAAI,OAAO,GAAG;AACV,UAAI,OAAO,GAAG;AACV,YAAI,IAAI,IAAI;AACZ,YAAI,IAAI,IAAI;AAAA,MAChB;AACA,YAAM;AACN,UAAI;AACJ,UAAI;AAAA,IACR;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,UAAU,UAAU;AACzB,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,MAAM,IAAI,UAAU,SAAU,MAAM;AACpC,QAAI,OAAO,KAAK,gBAAgB;AAChC,QAAI,IAAI,KAAK,qBAAqB;AAClC,QAAI,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,IAAI,EAAE,CAAC,IAAI;AAC9C,QAAI,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,EAAE,CAAC,IAAI;AAC/C,WAAO,KAAK,IAAI,GAAG,IAAI;AACvB,WAAO,KAAK,IAAI,GAAG,IAAI;AACvB,WAAO,KAAK,IAAI,GAAG,IAAI;AACvB,WAAO,KAAK,IAAI,GAAG,IAAI;AACvB,WAAO,CAAC,GAAG,CAAC;AAAA,EAChB,CAAC;AACD,MAAI,QAAQ,IAAI,KAAK,SAAU,IAAI,KAAK;AACpC,WAAO;AAAA,MACH;AAAA,MACA,GAAG,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,MAAM,MAAM,IAAI;AAAA,MAC/C,MAAM,SAAS,GAAG;AAAA,IACtB;AAAA,EACJ,CAAC;AACD,SAAO,MAAM,KAAK,SAAU,GAAG,GAAG;AAAE,WAAO,EAAE,IAAI,EAAE;AAAA,EAAG,CAAC,EAAE,IAAI,SAAU,MAAM;AAAE,WAAO,KAAK;AAAA,EAAM,CAAC;AACtG;AAEA,SAAS,kBAAkB,OAAO;AAC9B,SAAO,MAAM,MAAM,MAAM,MAAM,KAAK;AACxC;AACA,SAAS,oBAAoB;AACzB,SAAO;AAAA,IACH,iBAAiB,CAAC;AAAA,IAClB,eAAe,CAAC;AAAA,IAChB,OAAO;AAAA,EACX;AACJ;AACO,SAAS,aAAa,UAAU,QAAQ,eAAe;AAC1D,MAAI,eAAe,CAAC;AACpB,WAAS,YAAYQ,WAAU;AAC3B,aAASC,KAAI,GAAGA,KAAID,UAAS,QAAQC,MAAK;AACtC,UAAIC,QAAOF,UAASC,EAAC;AACrB,UAAI,kBAAkBC,KAAI,GAAG;AACzB,oBAAYA,MAAK,YAAY,CAAC;AAAA,MAClC,WACSA,iBAAgB,cAAM;AAC3B,qBAAa,KAAKA,KAAI;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AACA,cAAY,QAAQ;AACpB,MAAI,gBAAgB,aAAa;AACjC,MAAI,CAAC,eAAe;AAChB,WAAO,kBAAkB;AAAA,EAC7B;AACA,MAAI,aAAa,cAAc,cAAc;AAC7C,MAAI,gBAAgB,WAAW;AAAA,IAC3B,MAAM;AAAA,IAAQ,OAAO;AAAA,EACzB,CAAC;AACD,MAAI,cAAc,WAAW,eAAe;AACxC,YAAQ,MAAM,2CAA2C;AACzD,WAAO,kBAAkB;AAAA,EAC7B;AACA,iBAAe,UAAU,YAAY;AACrC,kBAAgB,UAAU,aAAa;AACvC,MAAI,UAAU,cAAc;AAC5B,MAAI,YAAY,cAAc;AAC9B,MAAI,kBAAkB,cAAc;AACpC,MAAI,oBAAoB,IAAI,sBAAc;AAC1C,WAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACpC,QAAI,OAAO,aAAa,CAAC;AACzB,QAAI,KAAK,cAAc,CAAC;AACxB,OAAG,SAAS;AACZ,OAAG,cAAc,iBAAiB;AAClC,QAAI,CAAC,iBAAiB;AAClB,uBAAiB,MAAM,EAAE;AAAA,IAC7B;AAAA,EACJ;AACA,SAAO,sBAAsB;AAC7B,SAAO,cAAc,WAAY;AAC7B,WAAO;AAAA,EACX;AACA,WAAS,qBAAqB,IAAI;AAC9B,aAASD,KAAI,GAAGA,KAAI,cAAc,QAAQA,MAAK;AAC3C,oBAAcA,EAAC,EAAE,YAAY,EAAE;AAAA,IACnC;AAAA,EACJ;AACA,sBAAoB,QAAQ,eAAe;AAAA,IACvC,OAAO,SAAU,IAAI;AACjB,2BAAqB,EAAE;AAAA,IAC3B;AAAA,EACJ,CAAC;AACD,sBAAoB,QAAQ,oBAAoB;AAAA,IAC5C,OAAO,SAAU,IAAI;AACjB,eAASA,KAAI,GAAGA,KAAI,cAAc,QAAQA,MAAK;AAC3C,sBAAcA,EAAC,EAAE,iBAAiB,EAAE;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,WAAS,gBAAgB;AACrB,WAAO,sBAAsB;AAC7B,WAAO,WAAW;AAClB,WAAO,cAAc;AACrB,kBAAc,QAAQ,aAAa;AACnC,kBAAc,QAAQ,kBAAkB;AAAA,EAC5C;AACA,MAAI,QAAQ,cAAc;AAC1B,MAAI,iBAAiB;AACjB,QAAI,cAAc;AAClB,QAAI,WAAW,WAAY;AACvB;AACA,UAAI,gBAAgB,GAAG;AACnB,sBAAc;AACd,mBAAW,QAAQ;AAAA,MACvB;AAAA,IACJ;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,UAAI,yBAAyB,kBAAkB,SAAS;AAAA,QACpD,QAAQ,cAAc,SAAS,KAAK,gBAAgB,GAAG,OAAO,aAAa,CAAC,GAAG,cAAc,CAAC,CAAC;AAAA,QAC/F,MAAM;AAAA,MACV,GAAG,aAAa,IAAI;AACpB,gBAAU,aAAa,CAAC,GAAG,cAAc,CAAC,GAAG,sBAAsB;AAAA,IACvE;AAAA,EACJ,OACK;AACD,WAAO,WAAW;AAClB,WAAO,UAAU;AAAA,MACb,UAAU;AAAA,IACd,GAAG,SAAS;AAAA,MACR,QAAQ,SAAU,GAAG;AACjB,iBAASA,KAAI,GAAGA,KAAI,OAAOA,MAAK;AAC5B,cAAI,QAAQ,cAAcA,EAAC;AAC3B,gBAAM,WAAW,OAAO;AACxB,gBAAM,WAAW;AAAA,QACrB;AACA,qBAAa,UAAU,CAAC;AAAA,MAC5B;AAAA,MACA,MAAM,WAAY;AACd,sBAAc;AACd,iBAASA,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACtC,wBAAc,SAASA,EAAC,GAAG,iBAAiB;AAAA,QAChD;AACA,mBAAW,QAAQ;AAAA,MACvB;AAAA,IACJ,GAAG,aAAa,CAAC;AAAA,EACrB;AACA,MAAI,OAAO,MAAM;AACb,yBAAqB,OAAO,IAAI;AAAA,EACpC;AACA,SAAO;AAAA,IACH,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,OAAO;AAAA,EACX;AACJ;AACO,SAAS,cAAc,UAAU,YAAY,eAAe;AAC/D,MAAI,QAAQ,WAAW;AACvB,MAAI,eAAe,CAAC;AACpB,MAAI,aAAa,cAAc,cAAc;AAC7C,WAAS,YAAY,UAAU;AAC3B,aAASA,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACtC,UAAI,OAAO,SAASA,EAAC;AACrB,UAAI,kBAAkB,IAAI,GAAG;AACzB,oBAAY,KAAK,YAAY,CAAC;AAAA,MAClC,WACS,gBAAgB,cAAM;AAC3B,qBAAa,KAAK,IAAI;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,kBAAkB,QAAQ,GAAG;AAC7B,gBAAY,SAAS,YAAY,CAAC;AAClC,QAAI,UAAU,aAAa;AAC3B,QAAI,UAAU,OAAO;AACjB,UAAI,IAAI;AACR,eAAS,IAAI,SAAS,IAAI,OAAO,KAAK;AAClC,qBAAa,KAAK,UAAU,aAAa,MAAM,OAAO,CAAC,CAAC;AAAA,MAC5D;AAAA,IACJ;AACA,iBAAa,SAAS;AAAA,EAC1B,OACK;AACD,mBAAe,WAAW,EAAE,MAAM,UAAU,OAAO,MAAM,CAAC;AAC1D,QAAI,oBAAoB,SAAS,qBAAqB;AACtD,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,mBAAa,CAAC,EAAE,kBAAkB,iBAAiB;AAAA,IACvD;AACA,QAAI,aAAa,WAAW,OAAO;AAC/B,cAAQ,MAAM,2CAA2C;AACzD,aAAO,kBAAkB;AAAA,IAC7B;AAAA,EACJ;AACA,iBAAe,UAAU,YAAY;AACrC,eAAa,UAAU,UAAU;AACjC,MAAI,kBAAkB,cAAc;AACpC,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,QAAI,yBAAyB,kBAAkB,SAAS;AAAA,MACpD,QAAQ,cAAc,SAAS,KAAK,gBAAgB,GAAG,OAAO,aAAa,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,IAChG,GAAG,aAAa,IAAI;AACpB,cAAU,aAAa,CAAC,GAAG,WAAW,CAAC,GAAG,sBAAsB;AAAA,EACpE;AACA,SAAO;AAAA,IACH,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,OAAO,WAAW;AAAA,EACtB;AACJ;;;AChiBA,SAAS,WAAW,UAAU;AAC5B,SAAO,QAAQ,SAAS,CAAC,CAAC;AAC5B;AACA,SAAS,oBAAoB,KAAK,MAAM;AACtC,MAAI,UAAU,CAAC;AACf,MAAI,aAAa,IAAI;AACrB,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,YAAQ,KAAK;AAAA,MACX,KAAK,IAAI,CAAC;AAAA,MACV,MAAM,CAAC;AAAA,IACT,CAAC;AAAA,EACH;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,MAAM,KAAK,CAAC,EAAE;AAClB,QAAI,IAAI;AACR,SAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,cAAQ,IAAI,UAAU,EAAE,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC;AAAA,IAC9C;AAAA,EACF;AACA,MAAI,MAAM;AAEV,WAAS,IAAI,aAAa,GAAG,KAAK,GAAG,KAAK;AACxC,QAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,QAAQ;AAC3B,UAAI,WAAW,QAAQ,GAAG,EAAE;AAC5B,UAAI,SAAS,UAAU,GAAG;AAGxB,YAAI,KAAK;AACP,gBAAM;AAAA,QACR,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,MAAM,SAAS;AACnB,UAAI,MAAM,KAAK,KAAK,MAAM,CAAC;AAC3B,cAAQ,CAAC,EAAE,OAAO,SAAS,MAAM,KAAK,GAAG;AACzC,cAAQ,GAAG,EAAE,OAAO,SAAS,MAAM,GAAG,GAAG;AACzC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,OAAO,SAAU,QAAQ;AACvB,QAAI,MAAM,CAAC;AAEX,QAAI,gBAAgB,IAAI,KAAK,IAAI,IAAI,OAAO,KAAK,MAAM,SAAS,IAAI,OAAO,KAAK;AAChF,aAAS,IAAI,GAAG,IAAI,OAAO,OAAO,KAAK;AACrC,UAAI,SAAS,UAAU,OAAO,IAAI;AAClC,aAAO,SAAS,WAAW,aAAa;AACxC,UAAI,KAAK,MAAM;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,OAAO;AACT;AACO,SAAS,oBAAoB,MAAM,IAAI,aAAa,aAAa,WAAW,mBAAmB;AACpG,MAAI,CAAC,KAAK,UAAU,CAAC,GAAG,QAAQ;AAC9B;AAAA,EACF;AACA,MAAI,qBAAqB,mBAAmB,UAAU,aAAa,SAAS;AAC5E,MAAI,EAAE,sBAAsB,mBAAmB,WAAW,IAAI;AAC5D;AAAA,EACF;AACA,MAAI,iBAAiB,YAAY,SAAS,qBAAqB,EAAE,IAAI,OAAO;AAC5E,MAAI,eAAe,OAAO,OAAO;AAAA;AAAA;AAAA,IAG/B,YAAY;AAAA,EACd,GAAG,kBAAkB;AACrB,MAAI;AACJ,MAAI;AACJ,MAAI,WAAW,IAAI,GAAG;AAEpB,WAAO;AACP,UAAM;AAAA,EACR;AACA,MAAI,WAAW,EAAE,GAAG;AAElB,WAAO;AACP,UAAM;AAAA,EACR;AACA,WAAS,cAAc,OAAOE,aAAYC,eAAcC,eAAc,cAAc;AAClF,QAAI,YAAY,MAAM;AACtB,QAAI,WAAW,MAAM;AACrB,QAAI,UAAU,WAAW,KAAK,CAAC,cAAc;AAE3C,UAAI,YAAYF,cAAa,UAAU,CAAC,IAAI;AAC5C,UAAI,UAAUA,cAAa,WAAW,UAAU,CAAC;AACjD,UAAI,kBAAkB,SAAS,GAAG;AAEhC,sBAAc;AAAA,UACZ,MAAM,CAAC,SAAS;AAAA,UAChB,KAAK;AAAA,QACP,GAAG,MAAMC,eAAcC,eAAc,IAAI;AAAA,MAC3C,OAAO;AACL,YAAI,yBAAyB,iBAAiB,SAAS;AAAA,UACrD,OAAO,eAAeD,eAAcC,aAAY;AAAA,QAClD,GAAG,YAAY,IAAI;AACnB,kBAAU,WAAW,SAAS,sBAAsB;AACpD,0BAAkB,WAAW,SAAS,WAAW,SAAS,sBAAsB;AAAA,MAClF;AAAA,IACF,OAAO;AACL,UAAI,uBAAuB,SAAS;AAAA,QAClC,YAAY,aAAa,WAAW;AAAA,QACpC,iBAAiB,kBAAkB,SAAU,KAAKC,QAAO,UAAU,QAAQ;AACzE,iBAAO,eAAe,MAAMF,eAAcC,aAAY;AAAA,QACxD;AAAA,MACF,GAAG,YAAY;AACf,UAAI,KAAKF,cAAa,aAAa,WAAW,UAAU,oBAAoB,IAAI,cAAc,UAAU,WAAW,oBAAoB,GACrI,kBAAkB,GAAG,iBACrB,gBAAgB,GAAG;AACrB,UAAI,QAAQ,gBAAgB;AAC5B,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAI,yBAAyB,iBAAiB,SAAS;AAAA,UACrD,OAAO,eAAe,GAAG,KAAK;AAAA,QAChC,GAAG,YAAY,IAAI;AACnB,0BAAkB,gBAAgB,CAAC,GAAG,cAAc,CAAC,GAAGA,cAAa,UAAU,CAAC,IAAI,MAAM,KAAKA,cAAa,MAAM,MAAM,UAAU,CAAC,GAAG,sBAAsB;AAAA,MAC9J;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,OAAO,SAAS,OAE/B,KAAK,SAAS,GAAG;AACnB,MAAI,eAAe,OAAO,oBAAoB,KAAK,IAAI,IAAI,oBAAoB,aAAa,KAAK,MAAM,CAAC,aAAa,OAAO,EAAE,CAAC;AAC/H,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,oBAAgB,aAAa,CAAC,EAAE,KAAK;AAAA,EACvC;AACA,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,kBAAc,aAAa,CAAC,GAAG,YAAY,cAAc,YAAY;AACrE,oBAAgB,aAAa,CAAC,EAAE,KAAK;AAAA,EACvC;AACF;AACO,SAAS,YAAY,UAAU;AACpC,MAAI,CAAC,UAAU;AACb,WAAO,CAAC;AAAA,EACV;AACA,MAAI,QAAQ,QAAQ,GAAG;AACrB,QAAI,aAAa,CAAC;AAClB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,iBAAW,KAAK,YAAY,SAAS,CAAC,CAAC,CAAC;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AACA,MAAI,WAAW,CAAC;AAChB,WAAS,SAAS,SAAU,IAAI;AAC9B,QAAI,cAAc,gBAAQ,CAAC,GAAG,mBAAmB,CAAC,GAAG,aAAa,CAAC,GAAG,QAAQ;AAC5E,eAAS,KAAK,EAAE;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;ACpJA,IAAI,uBAAuB;AAC3B,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AACrB,IAAI,iBAAiB;AAErB,IAAI,oCAAoC,UAAU;AAClD,SAAS,aAAa,MAAM,iBAAiB;AAC3C,MAAI,aAAa,KAAK;AACtB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,QAAI,UAAU,KAAK,iBAAiB,WAAW,CAAC,CAAC;AACjD,QAAI,WAAW,QAAQ,UAAU,eAAe,MAAM,GAAG;AACvD,aAAO,WAAW,CAAC;AAAA,IACrB;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,MAAM,WAAW,WAAW;AACvD,MAAI,UAAU,KAAK,iBAAiB,SAAS;AAC7C,MAAI,iBAAiB,WAAW,QAAQ;AACxC,MAAI,SAAS;AACX,QAAI,QAAQ,KAAK,IAAI,QAAQ,MAAM,SAAS;AAC5C,QAAI,gBAAgB;AAClB,aAAO,eAAe,WAAW,KAAK,KAAK,QAAQ;AAAA,IACrD;AACA,WAAO,QAAQ;AAAA,EACjB;AACF;AACA,SAAS,WAAW,MAAM,WAAW,aAAa,SAAS;AAEzD,MAAI,kBAAkB,UAAU,qBAAqB;AACrD,MAAI,aAAa,aAAa,MAAM,eAAe;AACnD,MAAI,YAAY;AACd,QAAI,UAAU,oBAAoB,MAAM,WAAW,UAAU;AAC7D,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,KAAK,eAAe,SAAS;AAC/C,MAAI,WAAW,UAAU,iBAAiB;AAC1C,MAAI,eAAe,YAAY,QAAQ,GAAG;AACxC,WAAO,YAAY,QAAQ,IAAI;AAAA,EACjC;AAEA,MAAI,SAAS;AACX;AAAA,EACF;AAEA,SAAO,eAAe,KAAK,MAAM,SAAS;AAC5C;AAEA,SAAS,qBAAqB,MAAM;AAClC,MAAI,QAAQ,CAAC;AACb,OAAK,MAAM,SAAU,YAAY;AAC/B,QAAI,OAAO,WAAW;AACtB,QAAI,cAAc,WAAW;AAC7B,QAAI,KAAK,MAAM,IAAI,sBAAsB;AACvC,UAAI,MAAuC;AACzC,aAAK,uDAAuD;AAAA,MAC9D;AACA;AAAA,IACF;AACA,QAAI,UAAU,KAAK,WAAW;AAC9B,aAAS,YAAY,GAAG,YAAY,QAAQ,QAAQ,aAAa;AAC/D,YAAM,KAAK;AAAA,QACT;AAAA,QACA,SAAS,WAAW,MAAM,WAAW,aAAa,KAAK;AAAA,QACvD,cAAc,WAAW,MAAM,WAAW,aAAa,IAAI;AAAA,QAC3D,QAAQ,WAAW;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,cAAc,OAAO,WAAW,UAAU;AACjD,QAAM,SAAS,SAAU,IAAI;AAC3B,QAAI,cAAc,cAAM;AAEtB,gBAAU,IAAI;AAAA,QACZ,OAAO;AAAA,UACL,SAAS;AAAA,QACX;AAAA,MACF,GAAG,WAAW;AAAA,QACZ,WAAW;AAAA,QACX,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AACA,SAAS,SAAS,IAAI;AACpB,MAAI,GAAG,QAAQ;AAGb,QAAI,oBAAoB,GAAG,qBAAqB;AAChD,OAAG,kBAAkB,iBAAiB;AACtC,OAAG,OAAO,OAAO,EAAE;AAAA,EACrB;AACF;AACA,SAAS,cAAc,IAAI;AACzB,KAAG,cAAc;AACjB,MAAI,GAAG,SAAS;AACd,OAAG,SAAS,SAAU,OAAO;AAC3B,YAAM,cAAc;AAAA,IACtB,CAAC;AAAA,EACH;AACF;AACA,SAAS,qBAAqB,IAAI,WAAW,aAAa;AACxD,MAAI,kBAAkB,mBAAmB,UAAU,aAAa,SAAS;AACzE,qBAAmB,GAAG,SAAS,SAAU,OAAO;AAC9C,QAAI,iBAAiB,qBAAa;AAChC,UAAI,WAAW,YAAY,KAAK;AAChC,UAAI,UAAU;AACZ,cAAM,YAAY;AAAA,UAChB,OAAO;AAAA,QACT,GAAG,eAAe;AAAA,MACpB;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,SAAS,YAAY,cAAc,cAAc;AAC/C,MAAI,MAAM,aAAa;AACvB,MAAI,QAAQ,aAAa,QAAQ;AAC/B,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,UAAU,aAAa,CAAC;AAC5B,QAAI,UAAU,aAAa,CAAC;AAC5B,QAAI,QAAQ,KAAK,MAAM,QAAQ,SAAS,MAAM,QAAQ,KAAK,MAAM,QAAQ,SAAS,GAAG;AACnF,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,SAAS,SAAS,KAAK;AAChD,MAAI,eAAe,qBAAqB,OAAO;AAC/C,MAAI,eAAe,qBAAqB,OAAO;AAC/C,WAAS,wBAAwB,MAAM,IAAI,SAAS,OAAO,cAAc;AACvE,QAAI,WAAW,MAAM;AACnB,SAAG,YAAY;AAAA,QACb,OAAO,WAAW,YAAY,OAG5B,OAAO,OAAO,CAAC,GAAG,QAAQ,KAAK,GAAG,KAAK,KAAK,IAAI,KAAK;AAAA,MACzD,GAAG,YAAY;AAAA,IACjB;AAAA,EACF;AACA,MAAI,oBAAoB;AAyCxB,MAAI,YAAY;AAEhB,MAAI,cAAc,cAAc;AAChC,MAAI,mBAAmB,cAAc;AACrC,eAAa,QAAQ,SAAU,MAAM;AACnC,SAAK,WAAW,YAAY,IAAI,KAAK,SAAS,IAAI;AAClD,SAAK,gBAAgB,iBAAiB,IAAI,KAAK,cAAc,IAAI;AAAA,EACnE,CAAC;AAED,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,QAAI,aAAa,aAAa,CAAC,EAAE;AACjC,QAAI,iBAAiB,IAAI,UAAU,GAAG;AACpC,kBAAY;AACZ;AAAA,IACF;AACA,QAAI,kBAAkB,aAAa,CAAC,EAAE;AACtC,QAAI,mBAAmB,YAAY,IAAI,eAAe,GAAG;AACvD,kBAAY;AACZ;AAAA,IACF;AAAA,EACF;AACA,WAAS,gBAAgB,OAAO,WAAW;AACzC,WAAO,SAAU,UAAU;AACzB,UAAI,OAAO,SAAS;AACpB,UAAI,YAAY,SAAS;AAEzB,UAAI,WAAW;AACb,eAAO,KAAK,MAAM,SAAS;AAAA,MAC7B;AACA,UAAI,OAAO;AACT,eAAO,cAAc,iBAAiB,SAAS,eAAe,SAAS;AAAA,MACzE,OAAO;AACL,eAAO,cAAc,iBAAiB,SAAS,eAAe,SAAS;AAAA,MACzE;AAAA,IACF;AAAA,EACF;AAIA,MAAI,QAAQ,YAAY,cAAc,YAAY;AAClD,MAAI,wBAAwB,CAAC;AAC7B,MAAI,CAAC,OAAO;AAIV,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,UAAI,UAAU,aAAa,CAAC;AAC5B,UAAI,KAAK,QAAQ,KAAK,iBAAiB,QAAQ,SAAS;AACxD,UAAI,IAAI;AACN,8BAAsB,GAAG,EAAE,IAAI;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,WAAS,eAAe,UAAU,UAAU;AAC1C,QAAI,UAAU,aAAa,QAAQ;AACnC,QAAII,WAAU,aAAa,QAAQ;AACnC,QAAI,YAAYA,SAAQ,KAAK;AAE7B,QAAI,QAAQ,QAAQ,KAAK,iBAAiB,QAAQ,SAAS;AAC3D,QAAI,QAAQA,SAAQ,KAAK,iBAAiBA,SAAQ,SAAS;AAE3D,QAAI,UAAU,OAAO;AACnB,eAAS,qBAAqB,OAAOA,SAAQ,WAAW,SAAS;AACjE;AAAA,IACF;AACA;AAAA;AAAA,MAEA,SAAS,sBAAsB,MAAM,EAAE;AAAA,MAAG;AACxC;AAAA,IACF;AACA,QAAI,OAAO;AAIT,oBAAc,KAAK;AACnB,UAAI,OAAO;AACT,sBAAc,KAAK;AAEnB,iBAAS,KAAK;AACd,4BAAoB;AACpB,4BAAoB,YAAY,KAAK,GAAG,YAAY,KAAK,GAAGA,SAAQ,QAAQ,WAAW,UAAU,uBAAuB;AAAA,MAC1H,OAAO;AACL,sBAAc,OAAO,WAAW,QAAQ;AAAA,MAC1C;AAAA,IACF;AAAA,EAEF;AACA,MAAI,mBAAW,cAAc,cAAc,gBAAgB,MAAM,KAAK,GAAG,gBAAgB,OAAO,KAAK,GAAG,MAAM,UAAU,EAAE,OAAO,cAAc,EAAE,gBAAgB,SAAU,UAAU,YAAY;AAC/L,QAAIA,WAAU,aAAa,QAAQ;AACnC,QAAI,UAAUA,SAAQ;AACtB,QAAI,YAAY,QAAQ;AACxB,QAAI,QAAQ,QAAQ,iBAAiBA,SAAQ,SAAS;AACtD,QAAI,aAAa,OAAO,IAAI,YAAY,SAAU,KAAK;AACrD,aAAO,aAAa,GAAG,EAAE,KAAK,iBAAiB,aAAa,GAAG,EAAE,SAAS;AAAA,IAC5E,CAAC,GAAG,SAAU,OAAO;AACnB,aAAO,SAAS,UAAU,SAAS,CAAC,sBAAsB,MAAM,EAAE;AAAA,IACpE,CAAC;AACD,QAAI,OAAO;AACT,oBAAc,KAAK;AACnB,UAAI,WAAW,QAAQ;AAErB,aAAK,YAAY,SAAU,OAAO;AAChC,wBAAc,KAAK;AACnB,mBAAS,KAAK;AAAA,QAChB,CAAC;AACD,4BAAoB;AACpB,4BAAoB,YAAY,UAAU,GAAG,YAAY,KAAK,GAAGA,SAAQ,QAAQ,WAAW,UAAU,uBAAuB;AAAA,MAC/H,OAAO;AACL,sBAAc,OAAO,WAAWA,SAAQ,SAAS;AAAA,MACnD;AAAA,IACF;AAAA,EAEF,CAAC,EAAE,gBAAgB,SAAU,YAAY,UAAU;AACjD,QAAI,UAAU,aAAa,QAAQ;AACnC,QAAI,QAAQ,QAAQ,KAAK,iBAAiB,QAAQ,SAAS;AAE3D,QAAI,SAAS,sBAAsB,MAAM,EAAE,GAAG;AAC5C;AAAA,IACF;AACA,QAAI,aAAa,OAAO,IAAI,YAAY,SAAU,KAAK;AACrD,aAAO,aAAa,GAAG,EAAE,KAAK,iBAAiB,aAAa,GAAG,EAAE,SAAS;AAAA,IAC5E,CAAC,GAAG,SAAUC,KAAI;AAChB,aAAOA,OAAMA,QAAO;AAAA,IACtB,CAAC;AACD,QAAI,WAAW,aAAa,WAAW,CAAC,CAAC,EAAE,KAAK;AAChD,QAAI,WAAW,QAAQ;AACrB,WAAK,YAAY,SAAU,OAAO;AAChC,eAAO,cAAc,KAAK;AAAA,MAC5B,CAAC;AACD,UAAI,OAAO;AACT,sBAAc,KAAK;AAEnB,iBAAS,KAAK;AACd,4BAAoB;AACpB;AAAA,UAAoB,YAAY,KAAK;AAAA,UAAG,YAAY,UAAU;AAAA,UAAG,QAAQ;AAAA;AAAA,UAEzE;AAAA,UAAU,WAAW,CAAC;AAAA,UAAG;AAAA,QAAuB;AAAA,MAClD,OAAO;AACL,aAAK,YAAY,SAAU,OAAO;AAChC,iBAAO,cAAc,OAAO,UAAU,WAAW,CAAC,CAAC;AAAA,QACrD,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EAEF,CAAC,EAAE,iBAAiB,SAAU,YAAY,YAAY;AAGpD,QAAI,mBAAW,YAAY,YAAY,SAAU,QAAQ;AACvD,aAAO,aAAa,MAAM,EAAE,KAAK,MAAM,aAAa,MAAM,EAAE,SAAS;AAAA,IACvE,GAAG,SAAU,QAAQ;AACnB,aAAO,aAAa,MAAM,EAAE,KAAK,MAAM,aAAa,MAAM,EAAE,SAAS;AAAA,IACvE,CAAC,EAAE,OAAO,SAAU,UAAU,UAAU;AAEtC,qBAAe,WAAW,QAAQ,GAAG,WAAW,QAAQ,CAAC;AAAA,IAC3D,CAAC,EAAE,QAAQ;AAAA,EACb,CAAC,EAAE,QAAQ;AACX,MAAI,mBAAmB;AACrB,SAAK,SAAS,SAAU,IAAI;AAC1B,UAAI,OAAO,GAAG;AACd,UAAI,cAAc,KAAK;AACvB,UAAI,OAAO,eAAe,IAAI,qBAAqB,WAAW;AAC9D,UAAI,eAAe,mBAAmB,UAAU,aAAa,CAAC;AAC9D,UAAI,QAAQ,YAAY,mBAAmB,KAAK,gBAAgB,aAAa,WAAW,GAAG;AACzF,aAAK,MAAM,SAAS,SAAUA,KAAI;AAChC,cAAIA,eAAc,gBAAQ,CAACA,IAAG,UAAU,QAAQ;AAG9C,YAAAA,IAAG,YAAY;AAAA,cACb,OAAO;AAAA,gBACL,SAAS;AAAA,cACX;AAAA,YACF,GAAG,YAAY;AAAA,UACjB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,uBAAuB,QAAQ;AACtC,MAAI,YAAY,OAAO,SAAS,qBAAqB,EAAE,IAAI,WAAW;AACtE,MAAI,CAAC,WAAW;AAEd,WAAO,OAAO;AAAA,EAChB;AACA,SAAO;AACT;AACA,SAAS,8BAA8B,WAAW;AAChD,MAAI,QAAQ,SAAS,GAAG;AAEtB,WAAO,UAAU,KAAK,EAAE,KAAK,GAAG;AAAA,EAClC;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,MAAM;AACpC,MAAI,KAAK,WAAW;AAClB,WAAO,KAAK,UAAU,SAAS,qBAAqB,EAAE,IAAI,aAAa;AAAA,EACzE;AACF;AACA,SAAS,4BAA4B,aAAa,QAAQ;AACxD,MAAI,gBAAgB,cAAc;AAClC,MAAI,aAAa,cAAc;AAG/B,MAAI,qBAAqB,cAAc;AACvC,OAAK,YAAY,WAAW,SAAU,QAAQ,KAAK;AACjD,QAAI,iBAAiB,YAAY,gBAAgB,GAAG;AACpD,QAAI,UAAU,YAAY,QAAQ,GAAG;AACrC,QAAI,gBAAgB,uBAAuB,MAAM;AACjD,QAAI,mBAAmB,8BAA8B,aAAa;AAClE,eAAW,IAAI,kBAAkB;AAAA,MAC/B,aAAa;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AACD,QAAI,QAAQ,aAAa,GAAG;AAE1B,WAAK,eAAe,SAAU,KAAK;AACjC,2BAAmB,IAAI,KAAK;AAAA,UAC1B,KAAK;AAAA,UACL,aAAa;AAAA,UACb,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,WAAS,mCAAmC,kBAAkB;AAC5D,QAAI,cAAc,IAAI,gBAAgB,GAAG;AACvC,WAAK,iDAAiD,gBAAgB;AAAA,IACxE;AAAA,EACF;AACA,OAAK,OAAO,eAAe,SAAU,QAAQ;AAC3C,QAAI,OAAO,6BAA6B,KAAK,OAAO,mBAAmB,GAAG;AACxE,UAAI,iBAAiB,OAAO,IAAI,aAAa;AAC7C,UAAI,UAAU,OAAO,QAAQ;AAC7B,UAAI,gBAAgB,uBAAuB,MAAM;AACjD,UAAI,mBAAmB,8BAA8B,aAAa;AAElE,UAAI,UAAU,WAAW,IAAI,gBAAgB;AAE7C,UAAI,SAAS;AACX,YAAI,MAAuC;AACzC,6CAAmC,gBAAgB;AAAA,QACrD;AAEA,sBAAc,IAAI,kBAAkB;AAAA,UAClC,WAAW,CAAC;AAAA,YACV,aAAa,QAAQ;AAAA,YACrB,QAAQ,uBAAuB,QAAQ,IAAI;AAAA,YAC3C,MAAM,QAAQ;AAAA,UAChB,CAAC;AAAA,UACD,WAAW,CAAC;AAAA,YACV,aAAa;AAAA,YACb,QAAQ,uBAAuB,OAAO;AAAA,YACtC,MAAM;AAAA,UACR,CAAC;AAAA,QACH,CAAC;AAAA,MACH,OAAO;AAGL,YAAI,QAAQ,aAAa,GAAG;AAC1B,cAAI,MAAuC;AACzC,+CAAmC,gBAAgB;AAAA,UACrD;AACA,cAAI,cAAc,CAAC;AACnB,eAAK,eAAe,SAAU,KAAK;AACjC,gBAAIC,WAAU,WAAW,IAAI,GAAG;AAChC,gBAAIA,SAAQ,MAAM;AAChB,0BAAY,KAAK;AAAA,gBACf,aAAaA,SAAQ;AAAA,gBACrB,QAAQ,uBAAuBA,SAAQ,IAAI;AAAA,gBAC3C,MAAMA,SAAQ;AAAA,cAChB,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AACD,cAAI,YAAY,QAAQ;AACtB,0BAAc,IAAI,kBAAkB;AAAA,cAClC,WAAW;AAAA,cACX,WAAW,CAAC;AAAA,gBACV,aAAa;AAAA,gBACb,MAAM;AAAA,gBACN,QAAQ,uBAAuB,OAAO;AAAA,cACxC,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AAGL,cAAI,YAAY,mBAAmB,IAAI,aAAa;AACpD,cAAI,WAAW;AACb,gBAAI,QAAQ,cAAc,IAAI,UAAU,GAAG;AAC3C,gBAAI,CAAC,OAAO;AACV,sBAAQ;AAAA,gBACN,WAAW,CAAC;AAAA,kBACV,aAAa,UAAU;AAAA,kBACvB,MAAM,UAAU;AAAA,kBAChB,QAAQ,uBAAuB,UAAU,IAAI;AAAA,gBAC/C,CAAC;AAAA,gBACD,WAAW,CAAC;AAAA,cACd;AACA,4BAAc,IAAI,UAAU,KAAK,KAAK;AAAA,YACxC;AACA,kBAAM,UAAU,KAAK;AAAA,cACnB,aAAa;AAAA,cACb,MAAM;AAAA,cACN,QAAQ,uBAAuB,OAAO;AAAA,YACxC,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,YAAY,QAAQ,QAAQ;AACnC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,QAAQ,OAAO,eAAe,QAAQ,OAAO,gBAAgB,OAAO,CAAC,EAAE,eAAe,OAAO,YAAY,QAAQ,OAAO,aAAa,OAAO,CAAC,EAAE;AACnJ,QAAI,OAAO;AACT,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,wBAAwB,eAAe,aAAa,QAAQ,KAAK;AACxE,MAAI,OAAO,CAAC;AACZ,MAAI,KAAK,CAAC;AACV,OAAK,iBAAiB,cAAc,IAAI,GAAG,SAAU,QAAQ;AAC3D,QAAI,MAAM,YAAY,YAAY,WAAW,MAAM;AACnD,QAAI,OAAO,GAAG;AACZ,WAAK,KAAK;AAAA,QACR,aAAa,YAAY,gBAAgB,GAAG;AAAA,QAC5C,MAAM,YAAY,QAAQ,GAAG;AAAA;AAAA,QAE7B,QAAQ,uBAAuB,YAAY,QAAQ,GAAG,CAAC;AAAA,QACvD,YAAY,OAAO;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,OAAK,iBAAiB,cAAc,EAAE,GAAG,SAAU,QAAQ;AACzD,QAAI,MAAM,YAAY,OAAO,eAAe,MAAM;AAClD,QAAI,OAAO,GAAG;AACZ,UAAI,OAAO,OAAO,cAAc,GAAG,EAAE,QAAQ;AAC7C,SAAG,KAAK;AAAA,QACN,aAAa,YAAY,gBAAgB,GAAG;AAAA,QAC5C;AAAA,QACA,QAAQ,uBAAuB,IAAI;AAAA,QACnC,YAAY,OAAO;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,MAAI,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG;AACpC,sBAAkB,MAAM,IAAI,GAAG;AAAA,EACjC;AACF;AACO,SAAS,2BAA2B,WAAW;AACpD,YAAU,wBAAwB,uBAAuB,SAAU,SAAS,KAAK,QAAQ;AACvF,SAAK,iBAAiB,OAAO,gBAAgB,GAAG,SAAU,UAAU;AAClE,WAAK,iBAAiB,SAAS,EAAE,GAAG,SAAU,QAAQ;AACpD,YAAI,SAAS,OAAO;AACpB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,OAAO,eAAe,QAAQ,OAAO,gBAAgB,OAAO,CAAC,EAAE,eAAe,OAAO,YAAY,QAAQ,OAAO,aAAa,OAAO,CAAC,EAAE,IAAI;AAC7I,mBAAO,CAAC,EAAE,gCAAgC,IAAI;AAAA,UAChD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACD,YAAU,wBAAwB,qBAAqB,SAAU,SAAS,KAAK,QAAQ;AAErF,QAAI,cAAc,kCAAkC,GAAG;AAEvD,QAAI,YAAY,aAAa,OAAO,iBAAiB,OAAO,eAAe;AAGzE,UAAI,gBAAgB,OAAO;AAC3B,UAAI,eAAe;AACjB,aAAK,iBAAiB,aAAa,GAAG,SAAU,KAAK;AACnD,kCAAwB,KAAK,aAAa,QAAQ,GAAG;AAAA,QACvD,CAAC;AAAA,MACH,OAAO;AAEL,YAAI,kBAAkB,4BAA4B,aAAa,MAAM;AACrE,aAAK,gBAAgB,KAAK,GAAG,SAAU,KAAK;AAC1C,cAAI,QAAQ,gBAAgB,IAAI,GAAG;AACnC,4BAAkB,MAAM,WAAW,MAAM,WAAW,GAAG;AAAA,QACzD,CAAC;AAAA,MACH;AAEA,WAAK,OAAO,eAAe,SAAU,QAAQ;AAE3C,YAAI,OAAO,gCAAgC,GAAG;AAC5C,iBAAO,gCAAgC,IAAI;AAAA,QAC7C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI,YAAY,QAAQ,UAAU;AAClC,QAAI,cAAc,YAAY,YAAY,CAAC;AAC3C,QAAI,oBAAoB,YAAY,kBAAkB,CAAC;AACvD,QAAI,YAAY,YAAY,UAAU,CAAC;AACvC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,OAAO,UAAU,CAAC,EAAE,QAAQ;AAGhC,UAAI,KAAK,MAAM,IAAI,sBAAsB;AACvC,oBAAY,KAAK,UAAU,CAAC,CAAC;AAC7B,0BAAkB,KAAK,UAAU,CAAC,EAAE,IAAI,aAAa,CAAC;AACtD,kBAAU,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AC3kBA,IAAI,CAACC,QAAc,CAAC;AAGpB,IAAI,CAAC,OAAW,CAAC;AAUjB,IAAI,CAACA,UAAWA,UAAUA,UAAUA,UAAcA,UAAYA,UAAUA,WAAWA,WAAcA,WAAYA,WAAYA,WAAaA,WAAeA,WAAaA,WAAcA,WAAkBA,WAAoBA,WAAYA,WAAcA,WAAmBA,WAAiBA,WAAeA,SAAW,CAAC;AAe/S,IAAIA,SAAa;AAUjB,IAAIA,SAAc;AAQlB,IAAIA,QAAY;AAOhB,IAAIA,SAAmB;AASvB,IAAIA,SAAiB;AAQrB,IAAIA,SAAiB;AAQrB,IAAIA,SAAgB;AAKpB,IAAIA,SAAgB;AAKpB,IAAIA,SAAgB;AASpB,IAAIA,SAAoB;AASxB,IAAIA,SAAc;AAKlB,IAAIA,SAAc;AAKlB,IAAIA,SAAiB;AAKrB,IAAIA,SAAkB;AAKtB,IAAIA,SAAiB;AAKrB,IAAIA,SAAiB;AAKrB,IAAIA,SAAe;AAEnB,IAAIA,SAAiB;AAMrB,IAAIA,SAAuB;AAK3B,IAAIA,SAAuB;AAE3B,IAAIA,SAAkB;AAKtB,IAAIA,SAA4B;AAKhC,IAAIA,SAA2B;AAK/B,IAAIA,SAAa;AAOjB,IAAIA,SAAkB;AACtB,IAAIA,SAAgB;AAOpB,IAAI,0BAAmB;AAOvB,IAAI,kBAAW;", "names": ["x0", "y0", "x1", "y1", "startAngle", "endAngle", "cx", "cy", "rx", "ry", "len", "fromList", "i", "from", "fromIsMany", "animateIndex", "animateCount", "count", "newItem", "el", "oldData", "install"]}