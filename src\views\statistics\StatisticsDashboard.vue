<template>
  <div class="statistics-dashboard">
    <div class="page-header">
      <h2>数据看板</h2>
      <div>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange"
        />
        <el-button type="primary" @click="handleRefresh" style="margin-left: 10px;">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <el-row :gutter="20" class="metrics-row">
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-number">{{ metrics.totalComplaints }}</div>
              <div class="metric-label">总投诉数</div>
              <div class="metric-trend">
                <span :class="metrics.complaintsTrend > 0 ? 'trend-up' : 'trend-down'">
                  {{ metrics.complaintsTrend > 0 ? '↑' : '↓' }} {{ Math.abs(metrics.complaintsTrend) }}%
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon pending">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-number">{{ metrics.pendingComplaints }}</div>
              <div class="metric-label">待处理投诉</div>
              <div class="metric-trend">
                <span :class="metrics.pendingTrend > 0 ? 'trend-up' : 'trend-down'">
                  {{ metrics.pendingTrend > 0 ? '↑' : '↓' }} {{ Math.abs(metrics.pendingTrend) }}%
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-number">{{ metrics.defectRate }}%</div>
              <div class="metric-label">平均不良率</div>
              <div class="metric-trend">
                <span :class="metrics.rateTrend > 0 ? 'trend-up' : 'trend-down'">
                  {{ metrics.rateTrend > 0 ? '↑' : '↓' }} {{ Math.abs(metrics.rateTrend) }}%
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon cost">
              <el-icon><Money /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-number">¥{{ metrics.totalCost.toLocaleString() }}</div>
              <div class="metric-label">总返工成本</div>
              <div class="metric-trend">
                <span :class="metrics.costTrend > 0 ? 'trend-up' : 'trend-down'">
                  {{ metrics.costTrend > 0 ? '↑' : '↓' }} {{ Math.abs(metrics.costTrend) }}%
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 投诉趋势图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>投诉趋势分析</span>
              <el-select v-model="trendPeriod" size="small" style="width: 100px;">
                <el-option label="日" value="day" />
                <el-option label="周" value="week" />
                <el-option label="月" value="month" />
              </el-select>
            </div>
          </template>
          <div class="chart-container">
            <v-chart :option="trendChartOption" style="height: 300px;" />
          </div>
        </el-card>
      </el-col>
      
      <!-- 不良分类分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>不良分类分布</span>
          </template>
          <div class="chart-container">
            <v-chart :option="categoryChartOption" style="height: 300px;" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <!-- 客户投诉排名 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>客户投诉排名 TOP10</span>
          </template>
          <div class="chart-container">
            <v-chart :option="customerChartOption" style="height: 300px;" />
          </div>
        </el-card>
      </el-col>
      
      <!-- 产品不良率 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>产品不良率分析</span>
          </template>
          <div class="chart-container">
            <v-chart :option="productChartOption" style="height: 300px;" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-row :gutter="20" class="table-row">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最新投诉动态</span>
              <el-button type="text" @click="$router.push('/complaints/list')">查看全部</el-button>
            </div>
          </template>
          <el-table :data="recentComplaints" style="width: 100%">
            <el-table-column prop="complaintNo" label="投诉编号" width="120" />
            <el-table-column prop="customerName" label="客户名称" width="140" />
            <el-table-column prop="productName" label="产品名称" width="120" />
            <el-table-column prop="defectCategory" label="不良分类" width="100">
              <template #default="scope">
                <el-tag size="small" :type="getDefectCategoryColor(scope.row.defectCategory)">
                  {{ scope.row.defectCategory }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="severity" label="严重度" width="80">
              <template #default="scope">
                <el-tag size="small" :type="getSeverityColor(scope.row.severity)">
                  {{ scope.row.severity }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template #default="scope">
                <el-tag size="small" :type="getStatusColor(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="complaintDate" label="投诉日期" width="110" />
            <el-table-column prop="defectRate" label="不良率" width="80">
              <template #default="scope">{{ scope.row.defectRate }}%</template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="text" size="small" @click="viewComplaint(scope.row.complaintNo)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { ElMessage } from 'element-plus'

use([
  CanvasRenderer,
  LineChart,
  BarChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 日期范围
const dateRange = ref([])
const trendPeriod = ref('month')

// 核心指标
const metrics = ref({
  totalComplaints: 45,
  complaintsTrend: -8.5,
  pendingComplaints: 12,
  pendingTrend: 15.2,
  defectRate: 3.2,
  rateTrend: -2.1,
  totalCost: 125600,
  costTrend: 12.8
})

// 最新投诉数据
const recentComplaints = ref([
  {
    complaintNo: 'CP2024001',
    customerName: '北京汽车制造有限公司',
    productName: '发动机总成',
    defectCategory: '结构问题',
    severity: '严重',
    status: '待处理',
    complaintDate: '2024-01-15',
    defectRate: 2.5
  },
  {
    complaintNo: 'CP2024002',
    customerName: '上海汽车工业集团',
    productName: '刹车系统',
    defectCategory: '功能性问题',
    severity: '紧急',
    status: '处理中',
    complaintDate: '2024-01-14',
    defectRate: 1.8
  },
  {
    complaintNo: 'CP2024003',
    customerName: '广州汽车集团',
    productName: '空调系统',
    defectCategory: '电气问题',
    severity: '一般',
    status: '已解决',
    complaintDate: '2024-01-13',
    defectRate: 4.2
  },
  {
    complaintNo: 'CP2024004',
    customerName: '深圳汽车技术有限公司',
    productName: '变速箱',
    defectCategory: '结构问题',
    severity: '轻微',
    status: '处理中',
    complaintDate: '2024-01-12',
    defectRate: 1.2
  },
  {
    complaintNo: 'CP2024005',
    customerName: '天津汽车制造厂',
    productName: '车身外壳',
    defectCategory: '外观问题',
    severity: '一般',
    status: '已关闭',
    complaintDate: '2024-01-11',
    defectRate: 6.8
  }
])

// 投诉趋势图配置
const trendChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['投诉数量', '不良率']
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: [
    {
      type: 'value',
      name: '投诉数量',
      position: 'left'
    },
    {
      type: 'value',
      name: '不良率(%)',
      position: 'right'
    }
  ],
  series: [
    {
      name: '投诉数量',
      type: 'bar',
      data: [12, 8, 15, 10, 18, 6],
      itemStyle: {
        color: '#409EFF'
      }
    },
    {
      name: '不良率',
      type: 'line',
      yAxisIndex: 1,
      data: [3.2, 2.8, 4.1, 2.9, 5.2, 2.1],
      itemStyle: {
        color: '#F56C6C'
      }
    }
  ]
}))

// 不良分类分布图配置
const categoryChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '不良分类',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 15, name: '结构问题' },
        { value: 8, name: '电气问题' },
        { value: 12, name: '外观问题' },
        { value: 10, name: '功能性问题' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 客户投诉排名图配置
const customerChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'category',
    data: ['天津汽车', '深圳汽车', '广州汽车', '上海汽车', '北京汽车']
  },
  series: [
    {
      name: '投诉数量',
      type: 'bar',
      data: [5, 8, 12, 15, 18],
      itemStyle: {
        color: '#67C23A'
      }
    }
  ]
}))

// 产品不良率图配置
const productChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['发动机总成', '刹车系统', '空调系统', '变速箱', '车身外壳']
  },
  yAxis: {
    type: 'value',
    name: '不良率(%)'
  },
  series: [
    {
      name: '不良率',
      type: 'bar',
      data: [2.5, 1.8, 4.2, 1.2, 6.8],
      itemStyle: {
        color: '#E6A23C'
      }
    }
  ]
}))

// 获取不良分类颜色
const getDefectCategoryColor = (category: string) => {
  switch (category) {
    case '结构问题': return 'danger'
    case '电气问题': return 'warning'
    case '外观问题': return 'info'
    case '功能性问题': return 'success'
    default: return ''
  }
}

// 获取严重度颜色
const getSeverityColor = (severity: string) => {
  switch (severity) {
    case '轻微': return 'info'
    case '一般': return 'warning'
    case '严重': return 'danger'
    case '紧急': return 'danger'
    default: return ''
  }
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case '待处理': return 'danger'
    case '处理中': return 'warning'
    case '已解决': return 'success'
    case '已关闭': return 'info'
    default: return ''
  }
}

// 日期变化处理
const handleDateChange = (dates: any) => {
  console.log('日期范围变化:', dates)
  // 这里可以根据日期范围重新加载数据
}

// 刷新数据
const handleRefresh = () => {
  ElMessage.success('数据已刷新')
  // 这里可以重新加载所有数据
}

// 查看投诉
const viewComplaint = (complaintNo: string) => {
  ElMessage.info(`查看投诉 ${complaintNo}`)
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.statistics-dashboard {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.metrics-row {
  margin-bottom: 20px;
}

.metric-card {
  height: 120px;
}

.metric-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.metric-icon.total {
  background-color: #409eff;
}

.metric-icon.pending {
  background-color: #f56c6c;
}

.metric-icon.rate {
  background-color: #e6a23c;
}

.metric-icon.cost {
  background-color: #67c23a;
}

.metric-info {
  flex: 1;
}

.metric-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.metric-trend {
  font-size: 12px;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

.charts-row {
  margin-bottom: 20px;
}

.table-row {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  width: 100%;
}
</style>
