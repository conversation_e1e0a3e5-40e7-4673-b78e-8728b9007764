<template>
  <div class="complaints-list">
    <div class="page-header">
      <h2>客户投诉管理</h2>
      <el-button type="primary" @click="$router.push('/complaints/add')">
        <el-icon><Plus /></el-icon>
        新增投诉
      </el-button>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="投诉编号">
          <el-input v-model="searchForm.complaintNo" placeholder="请输入投诉编号" clearable />
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model="searchForm.customerName" placeholder="请输入客户名称" clearable />
        </el-form-item>
        <el-form-item label="产品型号">
          <el-select v-model="searchForm.productModel" placeholder="请选择产品型号" clearable>
            <el-option label="Model A" value="Model A" />
            <el-option label="Model B" value="Model B" />
            <el-option label="Model C" value="Model C" />
          </el-select>
        </el-form-item>
        <el-form-item label="投诉状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待处理" value="待处理" />
            <el-option label="处理中" value="处理中" />
            <el-option label="已解决" value="已解决" />
            <el-option label="已关闭" value="已关闭" />
          </el-select>
        </el-form-item>
        <el-form-item label="严重度">
          <el-select v-model="searchForm.severity" placeholder="请选择严重度" clearable>
            <el-option label="轻微" value="轻微" />
            <el-option label="一般" value="一般" />
            <el-option label="严重" value="严重" />
            <el-option label="紧急" value="紧急" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 投诉列表 -->
    <el-card class="table-card">
      <el-table :data="complaintsData" style="width: 100%" v-loading="loading">
        <el-table-column prop="complaintNo" label="投诉编号" width="120" />
        <el-table-column prop="customerName" label="客户名称" width="120" />
        <el-table-column prop="productName" label="产品名称" width="120" />
        <el-table-column prop="productModel" label="产品型号" width="100" />
        <el-table-column prop="complaintDate" label="投诉日期" width="110" />
        <el-table-column prop="defectDescription" label="不良描述" min-width="150" show-overflow-tooltip />
        <el-table-column prop="defectQuantity" label="不良数量" width="80" />
        <el-table-column prop="defectRate" label="不良率" width="80">
          <el-table-column prop="defectQuantity" label="不良数量" width="80" />
          <template #default="scope">
            {{ scope.row.defectRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="severity" label="严重度" width="80">
          <template #default="scope">
            <el-tag :type="getSeverityType(scope.row.severity)">{{ scope.row.severity }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleView(scope.row)">查看</el-button>
              <el-button type="warning" size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="success" size="small" @click="handleAnalysis(scope.row)">分析</el-button>
              <el-button type="danger" size="small" @click="handle8D(scope.row)">8D</el-button>
              <el-dropdown @command="(command) => handleMoreAction(command, scope.row)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="containment">围堵措施</el-dropdown-item>
                    <el-dropdown-item command="containment">改善报告</el-dropdown-item>
                    <el-dropdown-item command="close">关闭投诉</el-dropdown-item>
                    <el-dropdown-item command="export">导出详情</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 围堵措施对话框 -->
    <el-dialog v-model="containmentDialogVisible" title="围堵措施管理" width="900px">
      <div class="containment-header">
        <div class="header-info">
          <span>投诉编号：{{ containmentForm.complaintNo }}</span>
        </div>
        <el-button type="primary" size="small" @click="handleAddContainment">
          <el-icon><Plus /></el-icon>
          新增围堵
        </el-button>
      </div>

      <!-- 围堵措施列表 -->
      <div class="containment-list">
        <div v-for="(item, index) in containmentList" :key="index" class="containment-item">
          <div class="item-header">
            <span class="item-number">No{{ index + 1 }}</span>
            <el-button type="text" size="small" @click="handleRemoveContainment(index)" class="remove-btn">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>

          <el-row :gutter="16" class="item-content">
            <el-col :span="6">
              <div class="form-item">
                <label>地点</label>
                <el-select v-model="item.location" placeholder="仓库" style="width: 100%">
                  <el-option label="仓库" value="仓库" />
                  <el-option label="生产线" value="生产线" />
                  <el-option label="客户现场" value="客户现场" />
                  <el-option label="运输途中" value="运输途中" />
                  <el-option label="供应商" value="供应商" />
                </el-select>
              </div>
            </el-col>

            <el-col :span="4">
              <div class="form-item">
                <label>数量</label>
                <el-input-number v-model="item.quantity" :min="0" style="width: 100%" />
              </div>
            </el-col>

            <el-col :span="6">
              <div class="form-item">
                <label>责任人</label>
                <el-select v-model="item.responsible" placeholder="张三" style="width: 100%">
                  <el-option label="张三" value="张三" />
                  <el-option label="李四" value="李四" />
                  <el-option label="王五" value="王五" />
                  <el-option label="赵六" value="赵六" />
                </el-select>
              </div>
            </el-col>

            <el-col :span="4">
              <div class="form-item">
                <label>部门</label>
                <el-input v-model="item.department" placeholder="PE" />
              </div>
            </el-col>

            <el-col :span="4">
              <div class="form-item">
                <label>状态</label>
                <el-select v-model="item.status" placeholder="进展情况" style="width: 100%">
                  <el-option label="计划中" value="计划中" />
                  <el-option label="执行中" value="执行中" />
                  <el-option label="已完成" value="已完成" />
                  <el-option label="已暂停" value="已暂停" />
                </el-select>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="16" class="item-content">
            <el-col :span="12">
              <div class="form-item">
                <label>围堵措施</label>
                <el-input v-model="item.measures" placeholder="请输入具体的围堵措施" />
              </div>
            </el-col>

            <el-col :span="6">
              <div class="form-item">
                <label>预计完成时间</label>
                <el-date-picker
                  v-model="item.expectedTime"
                  type="date"
                  placeholder="请选择日期"
                  style="width: 100%"
                />
              </div>
            </el-col>

            <el-col :span="6">
              <div class="form-item">
                <label>围堵结果</label>
                <el-input v-model="item.result" placeholder="围堵结果" />
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="dialog-actions">
        <el-button type="primary" @click="handleAddResponsible">添加责任人</el-button>
        <el-button type="success" @click="handleSubmitContainment">提交申请</el-button>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="containmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleContainmentSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>



    <!-- 查看详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="投诉详情" width="800px">
      <div v-if="currentComplaint" class="complaint-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="投诉编号">{{ currentComplaint.complaintNo }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ currentComplaint.customerName }}</el-descriptions-item>
          <el-descriptions-item label="联系方式">{{ currentComplaint.contactInfo }}</el-descriptions-item>
          <el-descriptions-item label="客户地址">{{ currentComplaint.customerAddress }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ currentComplaint.productName }}</el-descriptions-item>
          <el-descriptions-item label="产品型号">{{ currentComplaint.productModel }}</el-descriptions-item>
          <el-descriptions-item label="生产批次">{{ currentComplaint.productionBatch }}</el-descriptions-item>
          <el-descriptions-item label="生产日期">{{ currentComplaint.productionDate }}</el-descriptions-item>
          <el-descriptions-item label="投诉日期">{{ currentComplaint.complaintDate }}</el-descriptions-item>
          <el-descriptions-item label="客户车型">{{ currentComplaint.customerVehicleType }}</el-descriptions-item>
          <el-descriptions-item label="工厂车型">{{ currentComplaint.factoryVehicleType }}</el-descriptions-item>
          <el-descriptions-item label="不良数量">{{ currentComplaint.defectQuantity }}</el-descriptions-item>
          <el-descriptions-item label="不良率">{{ currentComplaint.defectRate }}%</el-descriptions-item>
          <el-descriptions-item label="不良发生环节">{{ currentComplaint.defectStage }}</el-descriptions-item>
          <el-descriptions-item label="严重度">
            <el-tag :type="getSeverityType(currentComplaint.severity)">{{ currentComplaint.severity }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentComplaint.status)">{{ currentComplaint.status }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="不良描述" :span="2">{{ currentComplaint.defectDescription }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()

// 搜索表单
const searchForm = ref({
  complaintNo: '',
  customerName: '',
  productModel: '',
  status: '',
  severity: ''
})

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const complaintsData = ref([])
const loading = ref(false)

// 对话框
const viewDialogVisible = ref(false)
const containmentDialogVisible = ref(false)
const currentComplaint = ref(null)

// 围堵措施表单
const containmentFormRef = ref<FormInstance>()
const containmentForm = ref({
  complaintNo: ''
})

const containmentTemplate = ref('standard')
const containmentList = ref([
  {
    location: '仓库',
    quantity: 0,
    responsible: '张三',
    department: 'PE',
    status: '计划中',
    measures: '',
    expectedTime: '',
    result: ''
  }
])



// 示例数据
const mockData = [
  {
    id: 1,
    complaintNo: 'CP2024001',
    customerName: '北京汽车制造有限公司',
    contactInfo: '010-12345678',
    customerAddress: '北京市朝阳区xxx路xxx号',
    productName: '发动机总成',
    productModel: 'Model A',
    productionBatch: 'B20240101',
    productionDate: '2024-01-01',
    complaintDate: '2024-01-15',
    customerVehicleType: '轿车A型',
    factoryVehicleType: 'FA-001',
    defectDescription: '发动机启动时出现异响，影响正常使用',
    defectQuantity: 5,
    defectRate: 2.5,
    defectStage: '使用',
    severity: '严重',
    status: '待处理',
    containmentMeasures: '立即停止该批次产品发货，对库存产品进行全面检查',
    verificationResults: '已完成100%检查，发现3台同批次产品存在类似问题'
  },
  {
    id: 2,
    complaintNo: 'CP2024002',
    customerName: '上海汽车工业集团',
    contactInfo: '021-87654321',
    customerAddress: '上海市浦东新区xxx路xxx号',
    productName: '刹车系统',
    productModel: 'Model B',
    productionBatch: 'B20240102',
    productionDate: '2024-01-02',
    complaintDate: '2024-01-14',
    customerVehicleType: 'SUV B型',
    factoryVehicleType: 'FB-002',
    defectDescription: '刹车踏板行程过长，制动效果不佳',
    defectQuantity: 3,
    defectRate: 1.8,
    defectStage: '安装',
    severity: '紧急',
    status: '处理中',
    containmentMeasures: '紧急召回已发货产品，暂停生产线，启动应急预案',
    verificationResults: '已召回85%产品，其中60%确认存在问题，已全部更换'
  },
  {
    id: 3,
    complaintNo: 'CP2024003',
    customerName: '广州汽车集团',
    contactInfo: '020-11223344',
    customerAddress: '广州市天河区xxx路xxx号',
    productName: '空调系统',
    productModel: 'Model C',
    productionBatch: 'B20240103',
    productionDate: '2024-01-03',
    complaintDate: '2024-01-13',
    customerVehicleType: '商务车C型',
    factoryVehicleType: 'FC-003',
    defectDescription: '空调制冷效果差，温度控制不准确',
    defectQuantity: 8,
    defectRate: 4.2,
    defectStage: '生产',
    severity: '一般',
    status: '已解决',
    containmentMeasures: '调整生产工艺参数，加强出厂检测',
    verificationResults: '重新检测50台产品，制冷效果均达标，问题已解决'
  },
  {
    id: 4,
    complaintNo: 'CP2024004',
    customerName: '深圳汽车技术有限公司',
    contactInfo: '0755-99887766',
    customerAddress: '深圳市南山区xxx路xxx号',
    productName: '变速箱',
    productModel: 'Model A',
    productionBatch: 'B20240104',
    productionDate: '2024-01-04',
    complaintDate: '2024-01-12',
    customerVehicleType: '轿车D型',
    factoryVehicleType: 'FD-004',
    defectDescription: '变速箱换挡时出现顿挫感',
    defectQuantity: 2,
    defectRate: 1.2,
    defectStage: '运输',
    severity: '轻微',
    status: '处理中',
    containmentMeasures: '检查运输过程，加强包装保护措施',
    verificationResults: '运输环节检查完成，包装改进后无新增问题'
  },
  {
    id: 5,
    complaintNo: 'CP2024005',
    customerName: '天津汽车制造厂',
    contactInfo: '022-55443322',
    customerAddress: '天津市滨海新区xxx路xxx号',
    productName: '车身外壳',
    productModel: 'Model B',
    productionBatch: 'B20240105',
    productionDate: '2024-01-05',
    complaintDate: '2024-01-11',
    customerVehicleType: 'MPV E型',
    factoryVehicleType: 'FE-005',
    defectDescription: '车身漆面出现色差和划痕',
    defectQuantity: 12,
    defectRate: 6.8,
    defectStage: '生产',
    severity: '一般',
    status: '已关闭',
    containmentMeasures: '停止使用问题批次油漆，重新调配标准色彩',
    verificationResults: '重新喷漆后色差问题解决，客户验收通过'
  }
]

// 获取严重度类型
const getSeverityType = (severity: string) => {
  switch (severity) {
    case '轻微': return 'info'
    case '一般': return 'warning'
    case '严重': return 'danger'
    case '紧急': return 'danger'
    default: return ''
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '待处理': return 'danger'
    case '处理中': return 'warning'
    case '已解决': return 'success'
    case '已关闭': return 'info'
    default: return ''
  }
}

// 搜索
const handleSearch = () => {
  loading.value = true
  // 模拟搜索
  setTimeout(() => {
    loadData()
    loading.value = false
  }, 500)
}

// 重置
const handleReset = () => {
  searchForm.value = {
    complaintNo: '',
    customerName: '',
    productModel: '',
    status: '',
    severity: ''
  }
  loadData()
}

// 查看详情
const handleView = (row: any) => {
  router.push(`/complaints/detail/${row.id}`)
}

// 编辑
const handleEdit = (row: any) => {
  ElMessage.info('编辑功能开发中...')
}

// 分析
const handleAnalysis = (row: any) => {
  ElMessage.info('跳转到原因分析页面...')
}

// 8D处理
const handle8D = (row: any) => {
  ElMessage.info(`启动8D流程处理投诉：${row.complaintNo}`)
}

// 更多操作
const handleMoreAction = (command: string, row: any) => {
  if (command === 'close') {
    ElMessageBox.confirm('确认关闭此投诉吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      ElMessage.success('投诉已关闭')
    })
  } else if (command === 'export') {
    ElMessage.info('导出功能开发中...')
  } else if (command === 'containment') {
    handleContainment(row)
  }
}

// 围堵措施
const handleContainment = (row: any) => {
  containmentForm.value = {
    complaintNo: row.complaintNo
  }
  // 重置围堵措施列表
  containmentList.value = [
    {
      location: '仓库',
      quantity: 0,
      responsible: '张三',
      department: 'PE',
      status: '计划中',
      measures: '',
      expectedTime: '',
      result: ''
    }
  ]
  containmentDialogVisible.value = true
}

// 新增围堵措施
const handleAddContainment = () => {
  containmentList.value.push({
    location: '',
    quantity: 0,
    responsible: '',
    department: '',
    status: '计划中',
    measures: '',
    expectedTime: '',
    result: ''
  })
}

// 删除围堵措施
const handleRemoveContainment = (index: number) => {
  if (containmentList.value.length > 1) {
    containmentList.value.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一条围堵措施')
  }
}

// 添加责任人
const handleAddResponsible = () => {
  ElMessage.info('添加责任人功能开发中...')
}

// 提交申请
const handleSubmitContainment = () => {
  ElMessage.success('围堵措施申请已提交')
}



// 围堵措施提交
const handleContainmentSubmit = () => {
  // 验证必填字段
  const hasEmptyFields = containmentList.value.some(item =>
    !item.location || !item.responsible || !item.measures
  )

  if (hasEmptyFields) {
    ElMessage.error('请完善围堵措施信息')
    return
  }

  ElMessage.success('围堵措施保存成功')
  containmentDialogVisible.value = false
  loadData()
}



// 分页处理
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val
  loadData()
}

// 加载数据
const loadData = () => {
  complaintsData.value = mockData
  pagination.value.total = mockData.length
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.complaints-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.complaint-detail {
  padding: 20px 0;
}

/* 围堵措施样式 */
.containment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.template-switch {
  margin-left: 20px;
}

.containment-list {
  max-height: 500px;
  overflow-y: auto;
}

.containment-item {
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-number {
  font-weight: 500;
  color: #409eff;
}

.remove-btn {
  color: #f56c6c;
}

.item-content {
  margin-bottom: 12px;
}

.form-item {
  margin-bottom: 8px;
}

.form-item label {
  display: block;
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.dialog-actions {
  margin: 20px 0;
  text-align: center;
}

.dialog-actions .el-button {
  margin: 0 8px;
}
</style>
