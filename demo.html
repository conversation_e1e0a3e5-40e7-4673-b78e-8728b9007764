<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户投诉质量管理系统 - 原型演示</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <script src="https://unpkg.com/echarts@5/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .layout-container {
            height: 100vh;
            display: flex;
        }
        
        .sidebar {
            width: 250px;
            background-color: #304156;
            overflow: hidden;
        }
        
        .logo {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #2b3a4b;
            color: white;
            margin-bottom: 0;
        }
        
        .logo h2 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }
        
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            height: 60px;
            background-color: #fff;
            border-bottom: 1px solid #e6e6e6;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .main-content {
            flex: 1;
            background-color: #f5f5f5;
            padding: 20px;
            overflow-y: auto;
        }
        
        .page-title {
            margin-bottom: 20px;
            color: #303133;
            font-size: 24px;
            font-weight: 500;
        }
        
        .stats-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stats-card {
            flex: 1;
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,.12), 0 0 6px rgba(0,0,0,.04);
            padding: 20px;
            height: 120px;
        }
        
        .stats-content {
            display: flex;
            align-items: center;
            height: 100%;
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 24px;
            color: white;
        }
        
        .stats-icon.pending { background-color: #f56c6c; }
        .stats-icon.processing { background-color: #e6a23c; }
        .stats-icon.completed { background-color: #67c23a; }
        .stats-icon.cost { background-color: #409eff; }
        
        .stats-info {
            flex: 1;
        }
        
        .stats-number {
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            line-height: 1;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 14px;
            color: #909399;
        }
        
        .demo-section {
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,.12), 0 0 6px rgba(0,0,0,.04);
            margin-bottom: 20px;
            padding: 20px;
        }
        
        .demo-section h3 {
            margin-bottom: 15px;
            color: #409eff;
            font-size: 18px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            color: #67c23a;
        }
        
        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-menu li {
            border-bottom: 1px solid #3a4a5c;
        }
        
        .nav-menu a {
            display: block;
            padding: 15px 20px;
            color: #bfcbd9;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            background-color: #409eff;
            color: white;
        }
        
        .chart-container {
            width: 100%;
            height: 300px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="logo">
                    <h2>客户投诉质量管理系统</h2>
                </div>
                <ul class="nav-menu">
                    <li><a href="#" class="active">首页</a></li>
                    <li><a href="#">客户投诉管理</a></li>
                    <li><a href="#">返工数据管理</a></li>
                    <li><a href="#">原因分析对策</a></li>
                    <li><a href="#">统计分析报表</a></li>
                </ul>
            </div>
            
            <!-- 主内容区 -->
            <div class="main-container">
                <!-- 顶部导航 -->
                <div class="header">
                    <div>
                        <span>首页 / 数据看板</span>
                    </div>
                    <div>
                        <span>管理员</span>
                    </div>
                </div>
                
                <!-- 主要内容 -->
                <div class="main-content">
                    <h1 class="page-title">客户投诉质量管理系统</h1>
                    
                    <!-- 统计卡片 -->
                    <div class="stats-row">
                        <div class="stats-card">
                            <div class="stats-content">
                                <div class="stats-icon pending">⚠</div>
                                <div class="stats-info">
                                    <div class="stats-number">12</div>
                                    <div class="stats-label">待处理投诉</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stats-card">
                            <div class="stats-content">
                                <div class="stats-icon processing">⏳</div>
                                <div class="stats-info">
                                    <div class="stats-number">8</div>
                                    <div class="stats-label">处理中投诉</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stats-card">
                            <div class="stats-content">
                                <div class="stats-icon completed">✓</div>
                                <div class="stats-info">
                                    <div class="stats-number">45</div>
                                    <div class="stats-label">已完成投诉</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stats-card">
                            <div class="stats-content">
                                <div class="stats-icon cost">¥</div>
                                <div class="stats-info">
                                    <div class="stats-number">¥125,600</div>
                                    <div class="stats-label">本月返工成本</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 功能模块介绍 -->
                    <div class="demo-section">
                        <h3>1. 客户投诉管理模块</h3>
                        <ul class="feature-list">
                            <li><span class="feature-icon">✓</span> 投诉信息录入：支持投诉编号自动生成、客户信息、产品信息、不良描述等完整信息录入</li>
                            <li><span class="feature-icon">✓</span> 投诉状态管理：跟踪投诉处理进度（待处理、处理中、已解决、已关闭）</li>
                            <li><span class="feature-icon">✓</span> 附件上传：支持上传图片、视频、文档等证据文件</li>
                            <li><span class="feature-icon">✓</span> 历史查询：多维度搜索功能，支持按日期、客户、产品、型号等条件查询</li>
                        </ul>
                    </div>
                    
                    <div class="demo-section">
                        <h3>2. 客户返工数据集成模块</h3>
                        <ul class="feature-list">
                            <li><span class="feature-icon">✓</span> 数据接口配置：与MES/WMS系统集成，自动拉取返工数据</li>
                            <li><span class="feature-icon">✓</span> 返工类型管理：客户返工、我方挑选、第三方挑选、换货等类型区分</li>
                            <li><span class="feature-icon">✓</span> 返工成本追踪：详细记录运输费、人工费、物料费、检测费等各项成本</li>
                            <li><span class="feature-icon">✓</span> 数据关联：返工数据与投诉记录自动关联匹配</li>
                        </ul>
                    </div>
                    
                    <div class="demo-section">
                        <h3>3. 客户投诉原因分析与对策模块</h3>
                        <ul class="feature-list">
                            <li><span class="feature-icon">✓</span> 不良分类：标准化分类管理（结构问题、电气问题、外观问题、功能性问题）</li>
                            <li><span class="feature-icon">✓</span> 临时对策录入：记录立即缓解问题的临时措施</li>
                            <li><span class="feature-icon">✓</span> 根本原因分析：支持5Why分析法、鱼骨图等分析工具</li>
                            <li><span class="feature-icon">✓</span> 改善对策制定：制定长期有效的改善措施，指定责任部门和责任人</li>
                            <li><span class="feature-icon">✓</span> 对策执行追踪：跟踪对策执行状态和完成情况</li>
                        </ul>
                    </div>
                    
                    <div class="demo-section">
                        <h3>4. 统计分析与报表模块</h3>
                        <ul class="feature-list">
                            <li><span class="feature-icon">✓</span> 多维度统计分析：按日期、客户、产品、型号、工厂、不良类型等维度分析</li>
                            <li><span class="feature-icon">✓</span> 投诉趋势分析：投诉量趋势图、不良率趋势图，识别投诉高峰期</li>
                            <li><span class="feature-icon">✓</span> TOP问题柏拉图：不良类型、产品型号、根本原因柏拉图分析</li>
                            <li><span class="feature-icon">✓</span> 报表生成：客户投诉周报/月报、产品质量分析报告、返工成本分析报告</li>
                            <li><span class="feature-icon">✓</span> 图表可视化：柱状图、折线图、饼图、柏拉图等多种图表展示</li>
                        </ul>
                    </div>
                    
                    <!-- 图表演示 -->
                    <div class="demo-section">
                        <h3>数据可视化演示</h3>
                        <div class="chart-container" id="trendChart"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图表
        const chartDom = document.getElementById('trendChart');
        const myChart = echarts.init(chartDom);
        
        const option = {
            title: {
                text: '投诉趋势分析'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['投诉数量', '不良率']
            },
            xAxis: {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月']
            },
            yAxis: [
                {
                    type: 'value',
                    name: '投诉数量',
                    position: 'left'
                },
                {
                    type: 'value',
                    name: '不良率(%)',
                    position: 'right'
                }
            ],
            series: [
                {
                    name: '投诉数量',
                    type: 'bar',
                    data: [12, 8, 15, 10, 18, 6],
                    itemStyle: {
                        color: '#409EFF'
                    }
                },
                {
                    name: '不良率',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [3.2, 2.8, 4.1, 2.9, 5.2, 2.1],
                    itemStyle: {
                        color: '#F56C6C'
                    }
                }
            ]
        };
        
        myChart.setOption(option);
        
        // 响应式
        window.addEventListener('resize', function() {
            myChart.resize();
        });
    </script>
</body>
</html>
