<template>
  <div class="rework-cost">
    <div class="page-header">
      <h2>返工成本追踪</h2>
      <el-button type="primary" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出报表
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">¥{{ stats.totalCost.toLocaleString() }}</div>
              <div class="stats-label">总返工成本</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon month">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">¥{{ stats.monthCost.toLocaleString() }}</div>
              <div class="stats-label">本月成本</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon avg">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">¥{{ stats.avgCost.toLocaleString() }}</div>
              <div class="stats-label">平均单次成本</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon count">
              <el-icon><DataLine /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.reworkCount }}</div>
              <div class="stats-label">返工次数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="返工类型">
          <el-select v-model="searchForm.reworkType" placeholder="请选择返工类型" clearable>
            <el-option label="客户返工" value="客户返工" />
            <el-option label="我方挑选" value="我方挑选" />
            <el-option label="第三方挑选" value="第三方挑选" />
            <el-option label="换货" value="换货" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model="searchForm.customerName" placeholder="请输入客户名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 成本明细表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>成本明细</span>
          <el-button type="text" @click="handleAddCost">
            <el-icon><Plus /></el-icon>
            添加成本项
          </el-button>
        </div>
      </template>
      
      <el-table :data="costData" style="width: 100%" v-loading="loading" show-summary :summary-method="getSummaries">
        <el-table-column prop="reworkNo" label="返工编号" width="120" />
        <el-table-column prop="complaintNo" label="关联投诉" width="120">
          <template #default="scope">
            <el-link v-if="scope.row.complaintNo" type="primary">{{ scope.row.complaintNo }}</el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="客户名称" width="140" />
        <el-table-column prop="reworkType" label="返工类型" width="100">
          <template #default="scope">
            <el-tag :type="getReworkTypeColor(scope.row.reworkType)">{{ scope.row.reworkType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="transportCost" label="运输费(元)" width="100">
          <template #default="scope">
            ¥{{ scope.row.transportCost.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="laborCost" label="人工费(元)" width="100">
          <template #default="scope">
            ¥{{ scope.row.laborCost.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="materialCost" label="物料费(元)" width="100">
          <template #default="scope">
            ¥{{ scope.row.materialCost.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="testCost" label="检测费(元)" width="100">
          <template #default="scope">
            ¥{{ scope.row.testCost.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="otherCost" label="其他费用(元)" width="110">
          <template #default="scope">
            ¥{{ scope.row.otherCost.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="totalCost" label="总成本(元)" width="100" fixed="right">
          <template #default="scope">
            <strong>¥{{ scope.row.totalCost.toLocaleString() }}</strong>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleViewDetail(scope.row)">详情</el-button>
              <el-button type="warning" size="small" @click="handleEditCost(scope.row)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 成本详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="成本详情" width="700px">
      <div v-if="currentCost" class="cost-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="返工编号">{{ currentCost.reworkNo }}</el-descriptions-item>
          <el-descriptions-item label="关联投诉">{{ currentCost.complaintNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ currentCost.customerName }}</el-descriptions-item>
          <el-descriptions-item label="返工类型">
            <el-tag :type="getReworkTypeColor(currentCost.reworkType)">{{ currentCost.reworkType }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="返工日期">{{ currentCost.reworkDate }}</el-descriptions-item>
          <el-descriptions-item label="完成日期">{{ currentCost.completionDate || '-' }}</el-descriptions-item>
        </el-descriptions>
        
        <h4 style="margin: 20px 0 10px 0;">成本明细</h4>
        <el-table :data="[currentCost]" border>
          <el-table-column prop="transportCost" label="运输费" width="100">
            <template #default="scope">¥{{ scope.row.transportCost.toLocaleString() }}</template>
          </el-table-column>
          <el-table-column prop="laborCost" label="人工费" width="100">
            <template #default="scope">¥{{ scope.row.laborCost.toLocaleString() }}</template>
          </el-table-column>
          <el-table-column prop="materialCost" label="物料费" width="100">
            <template #default="scope">¥{{ scope.row.materialCost.toLocaleString() }}</template>
          </el-table-column>
          <el-table-column prop="testCost" label="检测费" width="100">
            <template #default="scope">¥{{ scope.row.testCost.toLocaleString() }}</template>
          </el-table-column>
          <el-table-column prop="otherCost" label="其他费用" width="100">
            <template #default="scope">¥{{ scope.row.otherCost.toLocaleString() }}</template>
          </el-table-column>
          <el-table-column prop="totalCost" label="总计" width="100">
            <template #default="scope">
              <strong style="color: #f56c6c;">¥{{ scope.row.totalCost.toLocaleString() }}</strong>
            </template>
          </el-table-column>
        </el-table>
        
        <div v-if="currentCost.costDescription" style="margin-top: 15px;">
          <h4>费用说明</h4>
          <p>{{ currentCost.costDescription }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 编辑成本对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑成本" width="600px">
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="100px">
        <el-form-item label="运输费" prop="transportCost">
          <el-input-number v-model="editForm.transportCost" :min="0" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="人工费" prop="laborCost">
          <el-input-number v-model="editForm.laborCost" :min="0" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="物料费" prop="materialCost">
          <el-input-number v-model="editForm.materialCost" :min="0" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="检测费" prop="testCost">
          <el-input-number v-model="editForm.testCost" :min="0" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="其他费用" prop="otherCost">
          <el-input-number v-model="editForm.otherCost" :min="0" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="费用说明">
          <el-input v-model="editForm.costDescription" type="textarea" :rows="3" placeholder="请输入费用说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// 统计数据
const stats = ref({
  totalCost: 65100,
  monthCost: 25600,
  avgCost: 13020,
  reworkCount: 5
})

// 搜索表单
const searchForm = ref({
  dateRange: [],
  reworkType: '',
  customerName: ''
})

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const costData = ref([])
const loading = ref(false)

// 对话框
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const currentCost = ref(null)

// 编辑表单
const editFormRef = ref<FormInstance>()
const editForm = ref({
  transportCost: 0,
  laborCost: 0,
  materialCost: 0,
  testCost: 0,
  otherCost: 0,
  costDescription: ''
})

const editRules: FormRules = {
  transportCost: [{ required: true, message: '请输入运输费', trigger: 'blur' }],
  laborCost: [{ required: true, message: '请输入人工费', trigger: 'blur' }],
  materialCost: [{ required: true, message: '请输入物料费', trigger: 'blur' }],
  testCost: [{ required: true, message: '请输入检测费', trigger: 'blur' }],
  otherCost: [{ required: true, message: '请输入其他费用', trigger: 'blur' }]
}

// 示例数据
const mockData = [
  {
    id: 1,
    reworkNo: 'RW2024001',
    complaintNo: 'CP2024001',
    customerName: '北京汽车制造有限公司',
    reworkType: '客户返工',
    reworkDate: '2024-01-16',
    completionDate: '2024-01-18',
    transportCost: 5000,
    laborCost: 8000,
    materialCost: 10000,
    testCost: 2000,
    otherCost: 600,
    totalCost: 25600,
    costDescription: '发动机异响问题返工，包含拆装、检测、更换零件等费用'
  },
  {
    id: 2,
    reworkNo: 'RW2024002',
    complaintNo: 'CP2024002',
    customerName: '上海汽车工业集团',
    reworkType: '我方挑选',
    reworkDate: '2024-01-15',
    completionDate: '2024-01-16',
    transportCost: 0,
    laborCost: 6000,
    materialCost: 2000,
    testCost: 500,
    otherCost: 0,
    totalCost: 8500,
    costDescription: '批量挑选刹车系统不良品，主要为人工挑选费用'
  },
  {
    id: 3,
    reworkNo: 'RW2024003',
    complaintNo: '',
    customerName: '广州汽车集团',
    reworkType: '第三方挑选',
    reworkDate: '2024-01-14',
    completionDate: '2024-01-15',
    transportCost: 1000,
    laborCost: 0,
    materialCost: 0,
    testCost: 1000,
    otherCost: 10000,
    totalCost: 12000,
    costDescription: '委托第三方机构挑选空调系统，主要为第三方服务费'
  },
  {
    id: 4,
    reworkNo: 'RW2024004',
    complaintNo: 'CP2024004',
    customerName: '深圳汽车技术有限公司',
    reworkType: '换货',
    reworkDate: '2024-01-13',
    completionDate: '2024-01-14',
    transportCost: 3200,
    laborCost: 0,
    materialCost: 0,
    testCost: 0,
    otherCost: 0,
    totalCost: 3200,
    costDescription: '变速箱顿挫问题换货，仅承担物流费用不承担报废费用'
  },
  {
    id: 5,
    reworkNo: 'RW2024005',
    complaintNo: '',
    customerName: '天津汽车制造厂',
    reworkType: '我方挑选',
    reworkDate: '2024-01-12',
    completionDate: '2024-01-13',
    transportCost: 2000,
    laborCost: 8000,
    materialCost: 5000,
    testCost: 800,
    otherCost: 0,
    totalCost: 15800,
    costDescription: '车身漆面问题批量挑选，包含重新喷漆等费用'
  }
]

// 获取返工类型颜色
const getReworkTypeColor = (type: string) => {
  switch (type) {
    case '客户返工': return 'danger'
    case '我方挑选': return 'warning'
    case '第三方挑选': return 'info'
    case '换货': return 'success'
    default: return ''
  }
}

// 表格合计行
const getSummaries = (param: any) => {
  const { columns, data } = param
  const sums: any[] = []
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    if (['transportCost', 'laborCost', 'materialCost', 'testCost', 'otherCost', 'totalCost'].includes(column.property)) {
      const values = data.map((item: any) => Number(item[column.property]))
      if (!values.every((value: any) => Number.isNaN(value))) {
        sums[index] = `¥${values.reduce((prev: any, curr: any) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return prev + curr
          } else {
            return prev
          }
        }, 0).toLocaleString()}`
      } else {
        sums[index] = ''
      }
    } else {
      sums[index] = ''
    }
  })
  return sums
}

// 导出报表
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 添加成本项
const handleAddCost = () => {
  ElMessage.info('添加成本项功能开发中...')
}

// 搜索
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loadData()
    loading.value = false
  }, 500)
}

// 重置
const handleReset = () => {
  searchForm.value = {
    dateRange: [],
    reworkType: '',
    customerName: ''
  }
  loadData()
}

// 查看详情
const handleViewDetail = (row: any) => {
  currentCost.value = row
  detailDialogVisible.value = true
}

// 编辑成本
const handleEditCost = (row: any) => {
  currentCost.value = row
  editForm.value = {
    transportCost: row.transportCost,
    laborCost: row.laborCost,
    materialCost: row.materialCost,
    testCost: row.testCost,
    otherCost: row.otherCost,
    costDescription: row.costDescription
  }
  editDialogVisible.value = true
}

// 编辑提交
const handleEditSubmit = async () => {
  if (!editFormRef.value) return
  
  try {
    await editFormRef.value.validate()
    ElMessage.success('成本信息更新成功')
    editDialogVisible.value = false
    loadData()
  } catch (error) {
    ElMessage.error('请检查表单填写是否完整')
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val
  loadData()
}

// 加载数据
const loadData = () => {
  costData.value = mockData
  pagination.value.total = mockData.length
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.rework-cost {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background-color: #409eff;
}

.stats-icon.month {
  background-color: #67c23a;
}

.stats-icon.avg {
  background-color: #e6a23c;
}

.stats-icon.count {
  background-color: #f56c6c;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.cost-detail {
  padding: 20px 0;
}
</style>
