<template>
  <div class="statistics-trends">
    <div class="page-header">
      <h2>趋势分析</h2>
      <div>
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="分析维度">
          <el-select v-model="filterForm.dimension" placeholder="请选择分析维度">
            <el-option label="按日期" value="date" />
            <el-option label="按客户" value="customer" />
            <el-option label="按产品" value="product" />
            <el-option label="按不良类型" value="defectType" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间粒度">
          <el-select v-model="filterForm.timeGranularity" placeholder="请选择时间粒度">
            <el-option label="按日" value="day" />
            <el-option label="按周" value="week" />
            <el-option label="按月" value="month" />
            <el-option label="按季度" value="quarter" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleAnalyze">
            <el-icon><TrendCharts /></el-icon>
            分析
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 趋势图表 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 投诉量趋势 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>投诉量趋势图</span>
              <el-radio-group v-model="complaintTrendType" size="small">
                <el-radio-button label="line">折线图</el-radio-button>
                <el-radio-button label="bar">柱状图</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <v-chart :option="complaintTrendOption" style="height: 400px;" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <!-- 不良率趋势 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>不良率趋势图</span>
          </template>
          <div class="chart-container">
            <v-chart :option="defectRateTrendOption" style="height: 350px;" />
          </div>
        </el-card>
      </el-col>
      
      <!-- 成本趋势 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>返工成本趋势图</span>
          </template>
          <div class="chart-container">
            <v-chart :option="costTrendOption" style="height: 350px;" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 多维度对比分析 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>多维度对比分析</span>
              <el-select v-model="comparisonDimension" size="small" style="width: 120px;">
                <el-option label="按客户" value="customer" />
                <el-option label="按产品" value="product" />
                <el-option label="按工厂" value="factory" />
              </el-select>
            </div>
          </template>
          <div class="chart-container">
            <v-chart :option="comparisonOption" style="height: 400px;" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 趋势分析表格 -->
    <el-card class="table-card">
      <template #header>
        <span>趋势数据详情</span>
      </template>
      <el-table :data="trendData" style="width: 100%" v-loading="loading">
        <el-table-column prop="period" label="时间周期" width="120" />
        <el-table-column prop="complaintCount" label="投诉数量" width="100" />
        <el-table-column prop="defectRate" label="不良率(%)" width="100">
          <template #default="scope">
            {{ scope.row.defectRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="totalCost" label="返工成本(元)" width="120">
          <template #default="scope">
            ¥{{ scope.row.totalCost.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="avgResolutionTime" label="平均解决时间(天)" width="140" />
        <el-table-column prop="customerSatisfaction" label="客户满意度" width="120">
          <template #default="scope">
            <el-rate v-model="scope.row.customerSatisfaction" disabled show-score />
          </template>
        </el-table-column>
        <el-table-column prop="trend" label="趋势" width="120">
          <template #default="scope">
            <span :class="scope.row.trend === 'up' ? 'trend-up' : scope.row.trend === 'down' ? 'trend-down' : 'trend-stable'">
              {{ scope.row.trend === 'up' ? '↑ 上升' : scope.row.trend === 'down' ? '↓ 下降' : '→ 平稳' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注" min-width="150" show-overflow-tooltip />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { ElMessage } from 'element-plus'

use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 筛选表单
const filterForm = ref({
  dateRange: [],
  dimension: 'date',
  timeGranularity: 'month'
})

// 图表类型
const complaintTrendType = ref('line')
const comparisonDimension = ref('customer')

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const trendData = ref([])
const loading = ref(false)

// 示例数据
const mockTrendData = [
  {
    period: '2024-01',
    complaintCount: 12,
    defectRate: 3.2,
    totalCost: 45600,
    avgResolutionTime: 5.2,
    customerSatisfaction: 3.5,
    trend: 'down',
    remarks: '投诉数量较上月下降15%'
  },
  {
    period: '2024-02',
    complaintCount: 8,
    defectRate: 2.8,
    totalCost: 32400,
    avgResolutionTime: 4.8,
    customerSatisfaction: 4.0,
    trend: 'down',
    remarks: '持续改善，客户满意度提升'
  },
  {
    period: '2024-03',
    complaintCount: 15,
    defectRate: 4.1,
    totalCost: 58200,
    avgResolutionTime: 6.1,
    customerSatisfaction: 3.2,
    trend: 'up',
    remarks: '新产品上线导致投诉增加'
  },
  {
    period: '2024-04',
    complaintCount: 10,
    defectRate: 2.9,
    totalCost: 38500,
    avgResolutionTime: 4.5,
    customerSatisfaction: 3.8,
    trend: 'down',
    remarks: '问题得到有效控制'
  },
  {
    period: '2024-05',
    complaintCount: 18,
    defectRate: 5.2,
    totalCost: 72800,
    avgResolutionTime: 7.2,
    customerSatisfaction: 2.8,
    trend: 'up',
    remarks: '供应商质量问题导致投诉激增'
  }
]

// 投诉量趋势图配置
const complaintTrendOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['投诉数量', '解决数量', '待处理数量']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '投诉数量',
      type: complaintTrendType.value,
      data: [12, 8, 15, 10, 18, 6],
      itemStyle: {
        color: '#409EFF'
      }
    },
    {
      name: '解决数量',
      type: complaintTrendType.value,
      data: [10, 7, 12, 9, 14, 5],
      itemStyle: {
        color: '#67C23A'
      }
    },
    {
      name: '待处理数量',
      type: complaintTrendType.value,
      data: [2, 1, 3, 1, 4, 1],
      itemStyle: {
        color: '#F56C6C'
      }
    }
  ]
}))

// 不良率趋势图配置
const defectRateTrendOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{b}<br/>{a}: {c}%'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value',
    name: '不良率(%)'
  },
  series: [
    {
      name: '不良率',
      type: 'line',
      data: [3.2, 2.8, 4.1, 2.9, 5.2, 2.1],
      itemStyle: {
        color: '#E6A23C'
      },
      areaStyle: {
        color: 'rgba(230, 162, 60, 0.2)'
      }
    }
  ]
}))

// 成本趋势图配置
const costTrendOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{b}<br/>{a}: ¥{c}'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value',
    name: '成本(元)'
  },
  series: [
    {
      name: '返工成本',
      type: 'bar',
      data: [45600, 32400, 58200, 38500, 72800, 28900],
      itemStyle: {
        color: '#909399'
      }
    }
  ]
}))

// 多维度对比图配置
const comparisonOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['北京汽车', '上海汽车', '广州汽车', '深圳汽车', '天津汽车']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '北京汽车',
      type: 'line',
      data: [3, 2, 4, 2, 5, 1]
    },
    {
      name: '上海汽车',
      type: 'line',
      data: [2, 1, 3, 2, 4, 2]
    },
    {
      name: '广州汽车',
      type: 'line',
      data: [4, 3, 5, 3, 6, 2]
    },
    {
      name: '深圳汽车',
      type: 'line',
      data: [2, 1, 2, 2, 2, 1]
    },
    {
      name: '天津汽车',
      type: 'line',
      data: [1, 1, 1, 1, 1, 0]
    }
  ]
}))

// 导出报告
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 分析
const handleAnalyze = () => {
  loading.value = true
  setTimeout(() => {
    loadData()
    loading.value = false
    ElMessage.success('分析完成')
  }, 1000)
}

// 重置
const handleReset = () => {
  filterForm.value = {
    dateRange: [],
    dimension: 'date',
    timeGranularity: 'month'
  }
  loadData()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val
  loadData()
}

// 加载数据
const loadData = () => {
  trendData.value = mockTrendData
  pagination.value.total = mockTrendData.length
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.statistics-trends {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.charts-row {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  width: 100%;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

.trend-stable {
  color: #909399;
}
</style>
