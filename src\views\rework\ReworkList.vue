<template>
  <div class="rework-list">
    <div class="page-header">
      <h2>返工数据管理</h2>
      <div>
        <el-button type="primary" @click="handleSync">
          <el-icon><Refresh /></el-icon>
          同步MES/WMS数据
        </el-button>
        <el-button type="success" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          手动添加
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="返工编号">
          <el-input v-model="searchForm.reworkNo" placeholder="请输入返工编号" clearable />
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model="searchForm.customerName" placeholder="请输入客户名称" clearable />
        </el-form-item>
        <el-form-item label="返工类型">
          <el-select v-model="searchForm.reworkType" placeholder="请选择返工类型" clearable>
            <el-option label="客户返工" value="客户返工" />
            <el-option label="我方挑选" value="我方挑选" />
            <el-option label="第三方挑选" value="第三方挑选" />
            <el-option label="换货" value="换货" />
          </el-select>
        </el-form-item>
        <el-form-item label="关联投诉">
          <el-input v-model="searchForm.complaintNo" placeholder="请输入投诉编号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 返工数据列表 -->
    <el-card class="table-card">
      <el-table :data="reworkData" style="width: 100%" v-loading="loading">
        <el-table-column prop="reworkNo" label="返工编号" width="120" />
        <el-table-column prop="complaintNo" label="关联投诉" width="120">
          <template #default="scope">
            <el-link v-if="scope.row.complaintNo" type="primary" @click="viewComplaint(scope.row.complaintNo)">
              {{ scope.row.complaintNo }}
            </el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="客户名称" width="140" />
        <el-table-column prop="productName" label="产品名称" width="120" />
        <el-table-column prop="productModel" label="产品型号" width="100" />
        <el-table-column prop="reworkType" label="返工类型" width="100">
          <template #default="scope">
            <el-tag :type="getReworkTypeColor(scope.row.reworkType)">{{ scope.row.reworkType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reworkQuantity" label="返工数量" width="80" />
        <el-table-column prop="reworkDate" label="返工日期" width="110" />
        <el-table-column prop="totalCost" label="总成本(元)" width="100">
          <template #default="scope">
            ¥{{ scope.row.totalCost.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="dataSource" label="数据来源" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.dataSource === 'MES/WMS' ? 'success' : 'warning'">
              {{ scope.row.dataSource }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleView(scope.row)">查看</el-button>
              <el-button type="warning" size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="success" size="small" @click="handleCostDetail(scope.row)">成本详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="返工详情" width="800px">
      <div v-if="currentRework" class="rework-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="返工编号">{{ currentRework.reworkNo }}</el-descriptions-item>
          <el-descriptions-item label="关联投诉">{{ currentRework.complaintNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ currentRework.customerName }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ currentRework.productName }}</el-descriptions-item>
          <el-descriptions-item label="产品型号">{{ currentRework.productModel }}</el-descriptions-item>
          <el-descriptions-item label="生产批次">{{ currentRework.productionBatch }}</el-descriptions-item>
          <el-descriptions-item label="返工类型">
            <el-tag :type="getReworkTypeColor(currentRework.reworkType)">{{ currentRework.reworkType }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="返工数量">{{ currentRework.reworkQuantity }}</el-descriptions-item>
          <el-descriptions-item label="返工日期">{{ currentRework.reworkDate }}</el-descriptions-item>
          <el-descriptions-item label="完成日期">{{ currentRework.completionDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="总成本">¥{{ currentRework.totalCost.toLocaleString() }}</el-descriptions-item>
          <el-descriptions-item label="数据来源">
            <el-tag :type="currentRework.dataSource === 'MES/WMS' ? 'success' : 'warning'">
              {{ currentRework.dataSource }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="返工说明" :span="2">{{ currentRework.reworkDescription || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 手动添加对话框 -->
    <el-dialog v-model="addDialogVisible" title="手动添加返工数据" width="600px">
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="100px">
        <el-form-item label="关联投诉" prop="complaintNo">
          <el-select v-model="addForm.complaintNo" placeholder="请选择关联投诉" clearable style="width: 100%">
            <el-option label="CP2024001 - 发动机异响" value="CP2024001" />
            <el-option label="CP2024002 - 刹车系统故障" value="CP2024002" />
            <el-option label="CP2024003 - 空调制冷效果差" value="CP2024003" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="addForm.customerName" placeholder="请输入客户名称" />
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="addForm.productName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="返工类型" prop="reworkType">
          <el-select v-model="addForm.reworkType" placeholder="请选择返工类型" style="width: 100%">
            <el-option label="客户返工" value="客户返工" />
            <el-option label="我方挑选" value="我方挑选" />
            <el-option label="第三方挑选" value="第三方挑选" />
            <el-option label="换货" value="换货" />
          </el-select>
        </el-form-item>
        <el-form-item label="返工数量" prop="reworkQuantity">
          <el-input-number v-model="addForm.reworkQuantity" :min="1" style="width: 100%" />
        </el-form-item>
        <el-form-item label="返工日期" prop="reworkDate">
          <el-date-picker v-model="addForm.reworkDate" type="date" style="width: 100%" />
        </el-form-item>
        <el-form-item label="返工说明">
          <el-input v-model="addForm.reworkDescription" type="textarea" :rows="3" placeholder="请输入返工说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// 搜索表单
const searchForm = ref({
  reworkNo: '',
  customerName: '',
  reworkType: '',
  complaintNo: ''
})

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const reworkData = ref([])
const loading = ref(false)

// 对话框
const viewDialogVisible = ref(false)
const addDialogVisible = ref(false)
const currentRework = ref(null)

// 添加表单
const addFormRef = ref<FormInstance>()
const addForm = ref({
  complaintNo: '',
  customerName: '',
  productName: '',
  reworkType: '',
  reworkQuantity: 1,
  reworkDate: '',
  reworkDescription: ''
})

const addRules: FormRules = {
  customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
  reworkType: [{ required: true, message: '请选择返工类型', trigger: 'change' }],
  reworkQuantity: [{ required: true, message: '请输入返工数量', trigger: 'blur' }],
  reworkDate: [{ required: true, message: '请选择返工日期', trigger: 'change' }]
}

// 示例数据
const mockData = [
  {
    id: 1,
    reworkNo: 'RW2024001',
    complaintNo: 'CP2024001',
    customerName: '北京汽车制造有限公司',
    productName: '发动机总成',
    productModel: 'Model A',
    productionBatch: 'B20240101',
    reworkType: '客户返工',
    reworkQuantity: 5,
    reworkDate: '2024-01-16',
    completionDate: '2024-01-18',
    totalCost: 25600,
    dataSource: 'MES/WMS',
    reworkDescription: '发动机异响问题返工维修'
  },
  {
    id: 2,
    reworkNo: 'RW2024002',
    complaintNo: 'CP2024002',
    customerName: '上海汽车工业集团',
    productName: '刹车系统',
    productModel: 'Model B',
    productionBatch: 'B20240102',
    reworkType: '我方挑选',
    reworkQuantity: 50,
    reworkDate: '2024-01-15',
    completionDate: '2024-01-16',
    totalCost: 8500,
    dataSource: '手动录入',
    reworkDescription: '批量挑选刹车系统不良品'
  },
  {
    id: 3,
    reworkNo: 'RW2024003',
    complaintNo: '',
    customerName: '广州汽车集团',
    productName: '空调系统',
    productModel: 'Model C',
    productionBatch: 'B20240103',
    reworkType: '第三方挑选',
    reworkQuantity: 20,
    reworkDate: '2024-01-14',
    completionDate: '2024-01-15',
    totalCost: 12000,
    dataSource: 'MES/WMS',
    reworkDescription: '委托第三方机构挑选空调系统'
  },
  {
    id: 4,
    reworkNo: 'RW2024004',
    complaintNo: 'CP2024004',
    customerName: '深圳汽车技术有限公司',
    productName: '变速箱',
    productModel: 'Model A',
    productionBatch: 'B20240104',
    reworkType: '换货',
    reworkQuantity: 2,
    reworkDate: '2024-01-13',
    completionDate: '2024-01-14',
    totalCost: 3200,
    dataSource: '手动录入',
    reworkDescription: '变速箱顿挫问题换货，仅承担物流费用'
  },
  {
    id: 5,
    reworkNo: 'RW2024005',
    complaintNo: '',
    customerName: '天津汽车制造厂',
    productName: '车身外壳',
    productModel: 'Model B',
    productionBatch: 'B20240105',
    reworkType: '我方挑选',
    reworkQuantity: 30,
    reworkDate: '2024-01-12',
    completionDate: '2024-01-13',
    totalCost: 15800,
    dataSource: 'MES/WMS',
    reworkDescription: '车身漆面问题批量挑选'
  }
]

// 获取返工类型颜色
const getReworkTypeColor = (type: string) => {
  switch (type) {
    case '客户返工': return 'danger'
    case '我方挑选': return 'warning'
    case '第三方挑选': return 'info'
    case '换货': return 'success'
    default: return ''
  }
}

// 同步数据
const handleSync = () => {
  ElMessageBox.confirm('确认同步MES/WMS系统数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    loading.value = true
    setTimeout(() => {
      ElMessage.success('数据同步成功')
      loadData()
      loading.value = false
    }, 2000)
  })
}

// 手动添加
const handleAdd = () => {
  addDialogVisible.value = true
}

// 搜索
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loadData()
    loading.value = false
  }, 500)
}

// 重置
const handleReset = () => {
  searchForm.value = {
    reworkNo: '',
    customerName: '',
    reworkType: '',
    complaintNo: ''
  }
  loadData()
}

// 查看详情
const handleView = (row: any) => {
  currentRework.value = row
  viewDialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  ElMessage.info('编辑功能开发中...')
}

// 成本详情
const handleCostDetail = (row: any) => {
  ElMessage.info('跳转到成本详情页面...')
}

// 查看投诉
const viewComplaint = (complaintNo: string) => {
  ElMessage.info(`查看投诉 ${complaintNo}`)
}

// 添加提交
const handleAddSubmit = async () => {
  if (!addFormRef.value) return
  
  try {
    await addFormRef.value.validate()
    ElMessage.success('返工数据添加成功')
    addDialogVisible.value = false
    loadData()
  } catch (error) {
    ElMessage.error('请检查表单填写是否完整')
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val
  loadData()
}

// 加载数据
const loadData = () => {
  reworkData.value = mockData
  pagination.value.total = mockData.length
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.rework-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.rework-detail {
  padding: 20px 0;
}
</style>
