<template>
  <div class="rework-list">
    <div class="page-header">
      <h2>返工数据管理</h2>
      <div>
        <el-button type="primary" @click="handleSync">
          <el-icon><Refresh /></el-icon>
          同步MES/WMS数据
        </el-button>
        <el-button type="success" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          手动添加
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="返工编号">
          <el-input v-model="searchForm.reworkNo" placeholder="请输入返工编号" clearable />
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model="searchForm.customerName" placeholder="请输入客户名称" clearable />
        </el-form-item>
        <el-form-item label="返工类型">
          <el-select v-model="searchForm.reworkType" placeholder="请选择返工类型" clearable>
            <el-option label="客户返工" value="客户返工" />
            <el-option label="我方挑选" value="我方挑选" />
            <el-option label="第三方挑选" value="第三方挑选" />
            <el-option label="换货" value="换货" />
          </el-select>
        </el-form-item>
        <el-form-item label="关联投诉">
          <el-input v-model="searchForm.complaintNo" placeholder="请输入投诉编号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 返工数据列表 -->
    <el-card class="table-card">
      <el-table :data="reworkData" style="width: 100%" v-loading="loading">
        <el-table-column prop="reworkNo" label="返工编号" width="120" />
        <el-table-column prop="complaintNo" label="关联投诉" width="120">
          <template #default="scope">
            <el-link v-if="scope.row.complaintNo" type="primary" @click="viewComplaint(scope.row.complaintNo)">
              {{ scope.row.complaintNo }}
            </el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="客户名称" width="140" />
        <el-table-column prop="productName" label="产品名称" width="120" />
        <el-table-column prop="productModel" label="产品型号" width="100" />
        <el-table-column prop="reworkType" label="返工类型" width="100">
          <template #default="scope">
            <el-tag :type="getReworkTypeColor(scope.row.reworkType)">{{ scope.row.reworkType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reworkQuantity" label="返工数量" width="80" />
        <el-table-column prop="reworkDate" label="返工日期" width="110" />
        <el-table-column prop="totalCost" label="总成本(元)" width="100">
          <template #default="scope">
            ¥{{ scope.row.totalCost.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="dataSource" label="数据来源" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.dataSource === 'MES/WMS' ? 'success' : 'warning'">
              {{ scope.row.dataSource }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleView(scope.row)">查看</el-button>
              <el-button type="warning" size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="success" size="small" @click="handleCostDetail(scope.row)">成本详情</el-button>
              <el-button type="danger" size="small" @click="handle8D(scope.row)">8D</el-button>
              <el-dropdown @command="(command) => handleMoreAction(command, scope.row)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="containment">围堵措施</el-dropdown-item>
                    <el-dropdown-item command="export">导出详情</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="返工详情" width="800px">
      <div v-if="currentRework" class="rework-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="返工编号">{{ currentRework.reworkNo }}</el-descriptions-item>
          <el-descriptions-item label="关联投诉">{{ currentRework.complaintNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ currentRework.customerName }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ currentRework.productName }}</el-descriptions-item>
          <el-descriptions-item label="产品型号">{{ currentRework.productModel }}</el-descriptions-item>
          <el-descriptions-item label="生产批次">{{ currentRework.productionBatch }}</el-descriptions-item>
          <el-descriptions-item label="返工类型">
            <el-tag :type="getReworkTypeColor(currentRework.reworkType)">{{ currentRework.reworkType }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="返工数量">{{ currentRework.reworkQuantity }}</el-descriptions-item>
          <el-descriptions-item label="返工日期">{{ currentRework.reworkDate }}</el-descriptions-item>
          <el-descriptions-item label="完成日期">{{ currentRework.completionDate || '-' }}</el-descriptions-item>
          <el-descriptions-item label="总成本">¥{{ currentRework.totalCost.toLocaleString() }}</el-descriptions-item>
          <el-descriptions-item label="数据来源">
            <el-tag :type="currentRework.dataSource === 'MES/WMS' ? 'success' : 'warning'">
              {{ currentRework.dataSource }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="返工说明" :span="2">{{ currentRework.reworkDescription || '-' }}</el-descriptions-item>
          <el-descriptions-item label="围堵措施" :span="2">{{ currentRework.containmentMeasures || '暂无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 围堵措施对话框 -->
    <el-dialog v-model="containmentDialogVisible" title="返工围堵措施管理" width="900px">
      <div class="containment-header">
        <div class="header-info">
          <span>返工编号：{{ containmentForm.reworkNo }}</span>
          <div class="template-switch">
            <el-radio-group v-model="containmentTemplate" size="small">
              <el-radio-button label="standard">标准模板</el-radio-button>
              <el-radio-button label="custom">客户模板</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <el-button type="primary" size="small" @click="handleAddContainment">
          <el-icon><Plus /></el-icon>
          新增围堵
        </el-button>
      </div>

      <!-- 围堵措施列表 -->
      <div class="containment-list">
        <div v-for="(item, index) in containmentList" :key="index" class="containment-item">
          <div class="item-header">
            <span class="item-number">No{{ index + 1 }}</span>
            <el-button type="text" size="small" @click="handleRemoveContainment(index)" class="remove-btn">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>

          <el-row :gutter="16" class="item-content">
            <el-col :span="6">
              <div class="form-item">
                <label>地点</label>
                <el-select v-model="item.location" placeholder="仓库" style="width: 100%">
                  <el-option label="仓库" value="仓库" />
                  <el-option label="生产线" value="生产线" />
                  <el-option label="客户现场" value="客户现场" />
                  <el-option label="运输途中" value="运输途中" />
                  <el-option label="供应商" value="供应商" />
                </el-select>
              </div>
            </el-col>

            <el-col :span="4">
              <div class="form-item">
                <label>数量</label>
                <el-input-number v-model="item.quantity" :min="0" style="width: 100%" />
              </div>
            </el-col>

            <el-col :span="6">
              <div class="form-item">
                <label>责任人</label>
                <el-select v-model="item.responsible" placeholder="张三" style="width: 100%">
                  <el-option label="张三" value="张三" />
                  <el-option label="李四" value="李四" />
                  <el-option label="王五" value="王五" />
                  <el-option label="赵六" value="赵六" />
                </el-select>
              </div>
            </el-col>

            <el-col :span="4">
              <div class="form-item">
                <label>部门</label>
                <el-input v-model="item.department" placeholder="PE" />
              </div>
            </el-col>

            <el-col :span="4">
              <div class="form-item">
                <label>状态</label>
                <el-select v-model="item.status" placeholder="进展情况" style="width: 100%">
                  <el-option label="计划中" value="计划中" />
                  <el-option label="执行中" value="执行中" />
                  <el-option label="已完成" value="已完成" />
                  <el-option label="已暂停" value="已暂停" />
                </el-select>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="16" class="item-content">
            <el-col :span="12">
              <div class="form-item">
                <label>围堵措施</label>
                <el-input v-model="item.measures" placeholder="请输入具体的围堵措施" />
              </div>
            </el-col>

            <el-col :span="6">
              <div class="form-item">
                <label>预计完成时间</label>
                <el-date-picker
                  v-model="item.expectedTime"
                  type="date"
                  placeholder="请选择日期"
                  style="width: 100%"
                />
              </div>
            </el-col>

            <el-col :span="6">
              <div class="form-item">
                <label>围堵结果</label>
                <el-input v-model="item.result" placeholder="围堵结果" />
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="dialog-actions">
        <el-button type="primary" @click="handleAddResponsible">添加责任人</el-button>
        <el-button type="success" @click="handleSubmitContainment">提交申请</el-button>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="containmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleContainmentSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 手动添加对话框 -->
    <el-dialog v-model="addDialogVisible" title="手动添加返工数据" width="600px">
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="100px">
        <el-form-item label="关联投诉" prop="complaintNo">
          <el-select v-model="addForm.complaintNo" placeholder="请选择关联投诉" clearable style="width: 100%">
            <el-option label="********* - 发动机异响" value="*********" />
            <el-option label="********* - 刹车系统故障" value="*********" />
            <el-option label="********* - 空调制冷效果差" value="*********" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="addForm.customerName" placeholder="请输入客户名称" />
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="addForm.productName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="返工类型" prop="reworkType">
          <el-select v-model="addForm.reworkType" placeholder="请选择返工类型" style="width: 100%">
            <el-option label="客户返工" value="客户返工" />
            <el-option label="我方挑选" value="我方挑选" />
            <el-option label="第三方挑选" value="第三方挑选" />
            <el-option label="换货" value="换货" />
          </el-select>
        </el-form-item>
        <el-form-item label="返工数量" prop="reworkQuantity">
          <el-input-number v-model="addForm.reworkQuantity" :min="1" style="width: 100%" />
        </el-form-item>
        <el-form-item label="返工日期" prop="reworkDate">
          <el-date-picker v-model="addForm.reworkDate" type="date" style="width: 100%" />
        </el-form-item>
        <el-form-item label="返工说明">
          <el-input v-model="addForm.reworkDescription" type="textarea" :rows="3" placeholder="请输入返工说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// 搜索表单
const searchForm = ref({
  reworkNo: '',
  customerName: '',
  reworkType: '',
  complaintNo: ''
})

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const reworkData = ref([])
const loading = ref(false)

// 对话框
const viewDialogVisible = ref(false)
const addDialogVisible = ref(false)
const containmentDialogVisible = ref(false)
const currentRework = ref(null)

// 围堵措施相关
const containmentForm = ref({
  reworkNo: ''
})

const containmentTemplate = ref('standard')
const containmentList = ref([
  {
    location: '仓库',
    quantity: 0,
    responsible: '张三',
    department: 'PE',
    status: '计划中',
    measures: '',
    expectedTime: '',
    result: ''
  }
])

// 添加表单
const addFormRef = ref<FormInstance>()
const addForm = ref({
  complaintNo: '',
  customerName: '',
  productName: '',
  reworkType: '',
  reworkQuantity: 1,
  reworkDate: '',
  reworkDescription: ''
})

const addRules: FormRules = {
  customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
  reworkType: [{ required: true, message: '请选择返工类型', trigger: 'change' }],
  reworkQuantity: [{ required: true, message: '请输入返工数量', trigger: 'blur' }],
  reworkDate: [{ required: true, message: '请选择返工日期', trigger: 'change' }]
}

// 示例数据
const mockData = [
  {
    id: 1,
    reworkNo: 'RW2024001',
    complaintNo: '*********',
    customerName: '北京汽车制造有限公司',
    productName: '发动机总成',
    productModel: 'Model A',
    productionBatch: 'B20240101',
    reworkType: '客户返工',
    reworkQuantity: 5,
    reworkDate: '2024-01-16',
    completionDate: '2024-01-18',
    totalCost: 25600,
    dataSource: 'MES/WMS',
    reworkDescription: '发动机异响问题返工维修',
    containmentMeasures: '立即停止该批次产品发货，对库存产品进行全面检查'
  },
  {
    id: 2,
    reworkNo: 'RW2024002',
    complaintNo: '*********',
    customerName: '上海汽车工业集团',
    productName: '刹车系统',
    productModel: 'Model B',
    productionBatch: 'B20240102',
    reworkType: '我方挑选',
    reworkQuantity: 50,
    reworkDate: '2024-01-15',
    completionDate: '2024-01-16',
    totalCost: 8500,
    dataSource: '手动录入',
    reworkDescription: '批量挑选刹车系统不良品',
    containmentMeasures: '暂停生产线，对所有刹车系统进行重新检验'
  },
  {
    id: 3,
    reworkNo: 'RW2024003',
    complaintNo: '',
    customerName: '广州汽车集团',
    productName: '空调系统',
    productModel: 'Model C',
    productionBatch: 'B20240103',
    reworkType: '第三方挑选',
    reworkQuantity: 20,
    reworkDate: '2024-01-14',
    completionDate: '2024-01-15',
    totalCost: 12000,
    dataSource: 'MES/WMS',
    reworkDescription: '委托第三方机构挑选空调系统',
    containmentMeasures: '委托第三方检测机构进行全面检验，暂停相关批次发货'
  },
  {
    id: 4,
    reworkNo: 'RW2024004',
    complaintNo: 'CP2024004',
    customerName: '深圳汽车技术有限公司',
    productName: '变速箱',
    productModel: 'Model A',
    productionBatch: 'B20240104',
    reworkType: '换货',
    reworkQuantity: 2,
    reworkDate: '2024-01-13',
    completionDate: '2024-01-14',
    totalCost: 3200,
    dataSource: '手动录入',
    reworkDescription: '变速箱顿挫问题换货，仅承担物流费用',
    containmentMeasures: '紧急召回同批次产品，检查变速箱装配工艺'
  },
  {
    id: 5,
    reworkNo: 'RW2024005',
    complaintNo: '',
    customerName: '天津汽车制造厂',
    productName: '车身外壳',
    productModel: 'Model B',
    productionBatch: 'B20240105',
    reworkType: '我方挑选',
    reworkQuantity: 30,
    reworkDate: '2024-01-12',
    completionDate: '2024-01-13',
    totalCost: 15800,
    dataSource: 'MES/WMS',
    reworkDescription: '车身漆面问题批量挑选',
    containmentMeasures: '停止使用问题批次油漆，重新调配标准色彩'
  }
]

// 获取返工类型颜色
const getReworkTypeColor = (type: string) => {
  switch (type) {
    case '客户返工': return 'danger'
    case '我方挑选': return 'warning'
    case '第三方挑选': return 'info'
    case '换货': return 'success'
    default: return ''
  }
}

// 同步数据
const handleSync = () => {
  ElMessageBox.confirm('确认同步MES/WMS系统数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    loading.value = true
    setTimeout(() => {
      ElMessage.success('数据同步成功')
      loadData()
      loading.value = false
    }, 2000)
  })
}

// 手动添加
const handleAdd = () => {
  addDialogVisible.value = true
}

// 搜索
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loadData()
    loading.value = false
  }, 500)
}

// 重置
const handleReset = () => {
  searchForm.value = {
    reworkNo: '',
    customerName: '',
    reworkType: '',
    complaintNo: ''
  }
  loadData()
}

// 查看详情
const handleView = (row: any) => {
  currentRework.value = row
  viewDialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  ElMessage.info('编辑功能开发中...')
}

// 成本详情
const handleCostDetail = (row: any) => {
  ElMessage.info('跳转到成本详情页面...')
}

// 8D处理
const handle8D = (row: any) => {
  ElMessage.info(`启动8D流程处理返工：${row.reworkNo}`)
}

// 查看投诉
const viewComplaint = (complaintNo: string) => {
  ElMessage.info(`查看投诉 ${complaintNo}`)
}

// 更多操作
const handleMoreAction = (command: string, row: any) => {
  if (command === 'containment') {
    handleContainment(row)
  } else if (command === 'export') {
    ElMessage.info('导出功能开发中...')
  }
}

// 围堵措施
const handleContainment = (row: any) => {
  containmentForm.value = {
    reworkNo: row.reworkNo
  }
  // 重置围堵措施列表
  containmentList.value = [
    {
      location: '仓库',
      quantity: 0,
      responsible: '张三',
      department: 'PE',
      status: '计划中',
      measures: '',
      expectedTime: '',
      result: ''
    }
  ]
  containmentDialogVisible.value = true
}

// 新增围堵措施
const handleAddContainment = () => {
  containmentList.value.push({
    location: '',
    quantity: 0,
    responsible: '',
    department: '',
    status: '计划中',
    measures: '',
    expectedTime: '',
    result: ''
  })
}

// 删除围堵措施
const handleRemoveContainment = (index: number) => {
  if (containmentList.value.length > 1) {
    containmentList.value.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一条围堵措施')
  }
}

// 添加责任人
const handleAddResponsible = () => {
  ElMessage.info('添加责任人功能开发中...')
}

// 提交申请
const handleSubmitContainment = () => {
  ElMessage.success('围堵措施申请已提交')
}

// 围堵措施提交
const handleContainmentSubmit = () => {
  // 验证必填字段
  const hasEmptyFields = containmentList.value.some(item =>
    !item.location || !item.responsible || !item.measures
  )

  if (hasEmptyFields) {
    ElMessage.error('请完善围堵措施信息')
    return
  }

  ElMessage.success('围堵措施保存成功')
  containmentDialogVisible.value = false
  loadData()
}

// 添加提交
const handleAddSubmit = async () => {
  if (!addFormRef.value) return
  
  try {
    await addFormRef.value.validate()
    ElMessage.success('返工数据添加成功')
    addDialogVisible.value = false
    loadData()
  } catch (error) {
    ElMessage.error('请检查表单填写是否完整')
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val
  loadData()
}

// 加载数据
const loadData = () => {
  reworkData.value = mockData
  pagination.value.total = mockData.length
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.rework-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.rework-detail {
  padding: 20px 0;
}

/* 围堵措施样式 */
.containment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.template-switch {
  margin-left: 20px;
}

.containment-list {
  max-height: 500px;
  overflow-y: auto;
}

.containment-item {
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-number {
  font-weight: 500;
  color: #409eff;
}

.remove-btn {
  color: #f56c6c;
}

.item-content {
  margin-bottom: 12px;
}

.form-item {
  margin-bottom: 8px;
}

.form-item label {
  display: block;
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.dialog-actions {
  margin: 20px 0;
  text-align: center;
}

.dialog-actions .el-button {
  margin: 0 8px;
}
</style>
